# Self-Healer System Architecture

> **📚 Complete documentation available in [Documentation/](Documentation/INDEX.md)**

## 🎯 System Overview

The Self-Healer system is a **comprehensive self-healing and self-improving platform** designed to automatically detect, analyze, and resolve issues in the N8N Builder workflow automation system. It operates as a continuous background service with advanced learning capabilities.

## 🚀 Quick Navigation

- **🏗️ [Complete Architecture Guide](Documentation/ARCHITECTURE.md)** - Full technical documentation
- **📖 [Main Documentation](Documentation/README.md)** - System overview and usage
- **📋 [Documentation Index](Documentation/INDEX.md)** - All documentation organized
- **🧠 [KnowledgeBase Integration](Documentation/INTEGRATION_COMPLETE.md)** - Advanced audit trail system

## ✨ Latest Features

### 🧠 KnowledgeBase Integration (NEW)
- **📊 Validity-Rated Solutions**: Every fix attempt rated for effectiveness
- **🔗 Hyperlinked Audit Trail**: Clickable session and error details
- **📚 Knowledge Accumulation**: Builds institutional knowledge over time
- **🎯 Intelligent Recommendations**: Suggests best solutions based on history

### 🔧 SelfHealed Flag System
- **📝 Audit Trail**: All healing actions logged with timestamps
- **🚫 Duplicate Prevention**: Skip already-processed errors
- **📊 Performance Tracking**: Easy visibility in standard logs
- **🔄 Resume Capability**: System knows where it left off

### 🎯 Simplified Error Detection
- **🔍 Reliable Patterns**: Match consistent log format
- **📦 Module Extraction**: Separate module name and error message
- **⚙️ Consistent Parsing**: Works with any error message format

For complete technical details, see the [full architecture documentation](Documentation/ARCHITECTURE.md).

## Core Components

### 1. Error Detection Layer

#### ErrorMonitor (`core/error_monitor.py`)
- **Purpose**: Continuously monitors error logs and system state
- **Key Features**:
  - File system watcher for `logs/errors.log` and `logs/n8n_builder.log`
  - Real-time error detection using simplified, reliable patterns
  - Integration with existing `EnhancedErrorHandler`
  - Pattern recognition for recurring issues
  - Severity assessment and prioritization
  - SelfHealed flag tracking to prevent duplicate processing
  - Healing action logging for complete audit trail

#### Error Classification
- Uses simplified, reliable error patterns that match consistent log format:
  ```python
  'error_log_entry': re.compile(r'\[.*?\]\s+ERROR\s+([^:]+):\s*(.+)', re.IGNORECASE)
  'critical_log_entry': re.compile(r'\[.*?\]\s+CRITICAL\s+([^:]+):\s*(.+)', re.IGNORECASE)
  ```
- Extracts module name and error message separately for better categorization
- Tracks error frequency and trends
- Identifies critical vs. recoverable errors
- Skips already-healed errors using SelfHealed flag detection

### 2. Context Analysis Layer

#### ContextAnalyzer (`core/context_analyzer.py`)
- **Purpose**: Gathers comprehensive context for detected errors
- **Key Features**:
  - Documentation retrieval from structured docs
  - Code context gathering using codebase retrieval
  - Dependency mapping and impact analysis
  - Historical error pattern analysis
  - System state assessment

#### Context Sources
- **Documentation**: Uses new Documentation/ structure
- **Code**: Leverages existing codebase retrieval engine
- **Logs**: Analyzes related log entries and patterns
- **System**: Monitors performance and resource metrics
- **History**: Reviews previous similar errors and solutions

### 3. Solution Generation Layer

#### SolutionGenerator (`core/solution_generator.py`)
- **Purpose**: Creates targeted solutions for detected errors
- **Key Features**:
  - Local LLM integration (mimo-vl-7b-rl)
  - Pattern-based solution templates
  - Multi-strategy approach (immediate, gradual, preventive)
  - Solution ranking and prioritization
  - Risk assessment for proposed changes

#### Solution Types
- **Immediate Fixes**: Quick patches for critical issues
- **Systematic Repairs**: Comprehensive solutions for root causes
- **Preventive Measures**: Changes to prevent future occurrences
- **Configuration Adjustments**: Parameter tuning and optimization
- **Code Improvements**: Refactoring and enhancement suggestions

### 4. Validation & Safety Layer

#### SolutionValidator (`core/solution_validator.py`)
- **Purpose**: Ensures solutions are safe and effective
- **Key Features**:
  - Isolated testing environment
  - Rollback capability for all changes
  - Impact assessment and risk evaluation
  - Integration with existing test suite
  - Gradual deployment with monitoring

#### Safety Mechanisms
- **Sandbox Testing**: Test solutions in isolated environment
- **Rollback Points**: Create restore points before changes
- **Impact Analysis**: Assess potential side effects
- **Gradual Deployment**: Phased implementation with monitoring
- **Emergency Stop**: Immediate halt capability for critical issues

### 5. Learning & Improvement Layer

#### LearningEngine (`core/learning_engine.py`)
- **Purpose**: Continuously improves system effectiveness
- **Key Features**:
  - Success/failure tracking for all solutions
  - Pattern learning and adaptation
  - Solution effectiveness scoring
  - Knowledge base updates
  - Predictive error prevention

#### Learning Mechanisms
- **Solution Effectiveness**: Track success rates by error type
- **Pattern Recognition**: Identify new error patterns
- **Context Correlation**: Learn which contexts predict success
- **Preventive Insights**: Develop proactive measures
- **System Evolution**: Adapt to changing system characteristics

## Integration Architecture

### Existing System Integration

#### Logging System
- Extends `logging_config.py` with self-healer specific loggers
- Integrates with specialized loggers for different components
- Maintains compatibility with existing log rotation and management

#### Error Handling
- Builds upon `EnhancedErrorHandler` for error classification
- Extends error patterns with self-healing specific categories
- Maintains existing error reporting and user guidance

#### Performance Monitoring
- Integrates with `performance_optimizer.py` for system metrics
- Uses existing performance tracking for solution validation
- Coordinates with monitoring for proactive issue detection

#### Project Management
- Uses `project_manager.py` for safe file operations
- Integrates with existing backup and versioning systems
- Maintains project integrity during healing operations

## SelfHealed Flag System Architecture

### Healing Action Tracking

The Self-Healer implements a comprehensive audit trail system that integrates seamlessly with the existing logging infrastructure:

#### Log Entry Format
```
[timestamp] INFO self_healer.action: SelfHealed - Error Title: One-sentence summary of action taken
[timestamp] INFO self_healer.action: SelfHealFailed - Error Title: Brief description of failure reason
```

#### Implementation Components

1. **Healing Action Logger** (`error_monitor.log_healing_action()`)
   - Appends healing results to main log file
   - Formats entries to match existing log structure
   - Truncates long descriptions to maintain readability

2. **Already-Healed Detection** (`error_monitor._is_error_already_healed()`)
   - Scans log lines for SelfHealed/SelfHealFailed markers
   - Prevents duplicate processing of resolved errors
   - Enables efficient restart and resume capability

3. **Statistics Extraction** (`error_monitor.get_healing_statistics_from_log()`)
   - Analyzes log file to extract healing metrics
   - Provides success rates and recent activity
   - Enables performance monitoring without external databases

### Benefits of Log-Based Tracking

- **Persistence**: Healing history survives system restarts and crashes
- **Transparency**: All healing actions are visible in standard log files
- **Integration**: Works seamlessly with existing log management tools
- **Simplicity**: No additional databases or storage systems required
- **Auditability**: Complete trail of all healing attempts and outcomes

### Data Flow Architecture

```
Error Detection → Already Healed? → Context Analysis → Solution Generation → Validation → Implementation → Log Healing Action → Learning
     ↑               ↓ (Skip)                                                                           ↓                    ↓
     ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←← Feedback Loop ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## Configuration and Control

### Configuration Files
- `config/healer_config.yaml`: Main configuration
- `config/error_patterns.yaml`: Error classification patterns
- `config/solution_templates.yaml`: Solution templates and strategies

### Control Interface
- REST API for manual control and monitoring
- Dashboard for real-time status and metrics
- CLI commands for administration and debugging

## Security and Safety

### Safety Measures
- All changes require validation before implementation
- Automatic rollback on failure detection
- Rate limiting to prevent system overload
- Emergency stop mechanisms for critical situations

### Security Considerations
- Secure handling of system credentials and sensitive data
- Audit logging for all healing activities
- Access control for manual interventions
- Isolation of testing environments

## Monitoring and Metrics

### Key Metrics
- Error detection rate and accuracy
- Solution success rate by category
- System healing time (detection to resolution)
- False positive/negative rates
- System stability improvements

### Dashboards
- Real-time healing activity monitor
- Historical trends and patterns
- System health overview
- Performance impact assessment

## Future Enhancements

### Planned Features
- Distributed healing across multiple instances
- Advanced ML models for pattern recognition
- Integration with external monitoring systems
- Automated documentation updates
- Predictive maintenance capabilities

This architecture provides a robust foundation for building a self-healing system that can automatically maintain and improve the N8N Builder system while ensuring safety and reliability.
