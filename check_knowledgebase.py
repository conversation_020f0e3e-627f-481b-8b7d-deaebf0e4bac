#!/usr/bin/env python3
"""
Script to check KnowledgeBase tables for Self-Healer data
"""
import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, '.')

from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def check_knowledgebase():
    """Check KnowledgeBase tables for Self-Healer related data."""
    
    print("🔍 Checking KnowledgeBase for Self-Healer data...")
    
    # Initialize the database tool
    db_tool = MCPDatabaseTool('knowledgebase')
    
    try:
        # First, let's check what tables exist
        print('\n=== Available Tables ===')
        result = await db_tool.execute_query("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            AND TABLE_NAME LIKE 'REF_%'
            ORDER BY TABLE_NAME
        """)
        print(result)

        # Check the structure of REF_Entities table
        print('\n=== REF_Entities Table Structure ===')
        result = await db_tool.execute_query("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'REF_Entities'
            ORDER BY ORDINAL_POSITION
        """)
        print(result)

        # Check REF_Entities table with correct column names
        print('\n=== REF_Entities Table (Latest 10) ===')
        result = await db_tool.execute_query('SELECT TOP 10 * FROM REF_Entities')
        print(result)
        
        # Check the structure of REF_Fact table
        print('\n=== REF_Fact Table Structure ===')
        result = await db_tool.execute_query("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'REF_Fact'
            ORDER BY ORDINAL_POSITION
        """)
        print(result)

        print('\n=== REF_Fact Table (Latest 10) ===')
        result = await db_tool.execute_query('SELECT TOP 10 * FROM REF_Fact')
        print(result)

        # Check the structure of REF_Opinion table
        print('\n=== REF_Opinion Table Structure ===')
        result = await db_tool.execute_query("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'REF_Opinion'
            ORDER BY ORDINAL_POSITION
        """)
        print(result)

        print('\n=== REF_Opinion Table (Latest 10) ===')
        result = await db_tool.execute_query('SELECT TOP 10 * FROM REF_Opinion')
        print(result)
        
        # Check for Self-Healer related entities
        print('\n=== Self-Healer Related Entities ===')
        result = await db_tool.execute_query("""
            SELECT * FROM REF_Entities 
            WHERE EntityName LIKE '%Self%Healer%' 
               OR EntityName LIKE '%N8N%Builder%'
               OR EntityName LIKE '%Error%'
               OR EntityName LIKE '%Healing%'
               OR EntityName LIKE '%Import%'
               OR EntityName LIKE '%Port%'
            ORDER BY EntityID DESC
        """)
        print(result)
        
        # Check for recent facts with entity names
        print('\n=== Recent Facts with Entity Names ===')
        result = await db_tool.execute_query("""
            SELECT TOP 10 f.FactID, f.EntityID, e.EntityName, f.FactText, f.ValidityRating, f.DateCreated
            FROM REF_Fact f
            LEFT JOIN REF_Entities e ON f.EntityID = e.EntityID
            ORDER BY f.FactID DESC
        """)
        print(result)
        
        # Check for recent opinions with entity names
        print('\n=== Recent Opinions with Entity Names ===')
        result = await db_tool.execute_query("""
            SELECT TOP 10 o.OpinionID, o.EntityID, e.EntityName, o.OpinionText, o.ValidityRating, o.DateCreated
            FROM REF_Opinion o
            LEFT JOIN REF_Entities e ON o.EntityID = e.EntityID
            ORDER BY o.OpinionID DESC
        """)
        print(result)
        
        # Check total counts
        print('\n=== Table Counts ===')
        result = await db_tool.execute_query("SELECT COUNT(*) as EntityCount FROM REF_Entities")
        print(f"Total Entities: {result}")
        
        result = await db_tool.execute_query("SELECT COUNT(*) as FactCount FROM REF_Fact")
        print(f"Total Facts: {result}")
        
        result = await db_tool.execute_query("SELECT COUNT(*) as OpinionCount FROM REF_Opinion")
        print(f"Total Opinions: {result}")
        
        # Check for today's entries
        print('\n=== Today\'s Entries ===')
        result = await db_tool.execute_query("""
            SELECT 'Facts' as TableName, COUNT(*) as TodayCount
            FROM REF_Fact 
            WHERE CAST(DateCreated AS DATE) = CAST(GETDATE() AS DATE)
            UNION ALL
            SELECT 'Opinions' as TableName, COUNT(*) as TodayCount
            FROM REF_Opinion 
            WHERE CAST(DateCreated AS DATE) = CAST(GETDATE() AS DATE)
            UNION ALL
            SELECT 'Entities' as TableName, COUNT(*) as TodayCount
            FROM REF_Entities 
            WHERE CAST(DateCreated AS DATE) = CAST(GETDATE() AS DATE)
        """)
        print(result)
        
    except Exception as e:
        print(f'❌ Error querying database: {e}')
        import traceback
        print(f'🔍 Full traceback: {traceback.format_exc()}')

if __name__ == "__main__":
    # Run the async function
    asyncio.run(check_knowledgebase())
