# 🚀 N8N_Builder Server Startup Methods

## 📋 Overview

N8N_Builder provides **two different methods** to start the local web server, each with distinct advantages and use cases. This document clarifies the differences and helps you choose the right method.

## 🔧 **Method A: Direct Python Execution (Recommended for Development)**

### **Command**
```bash
python run.py
```

### **Access URL**
- **http://localhost:8002**

### **Features**
- ✅ **Automatic Port Cleanup** - Kills existing processes on ports 8002 and 8080
- ✅ **Enhanced Error Handling** - Graceful shutdown and comprehensive error logging
- ✅ **Auto-reload Enabled** - Automatically restarts on code changes
- ✅ **Robust Process Management** - Better handling of interrupted sessions
- ✅ **Development Optimized** - Configured for development workflow

### **Configuration**
- **Port**: 8002 (hardcoded)
- **Host**: 127.0.0.1 (hardcoded)
- **Reload**: Always enabled
- **Log Level**: INFO with enhanced formatting

### **When to Use**
- ✅ **Development work** - Writing and testing code
- ✅ **Debugging sessions** - Need reliable restart behavior
- ✅ **Port conflicts** - Automatic cleanup prevents conflicts
- ✅ **Daily usage** - Most robust for regular use

## ⚙️ **Method B: CLI Command (Configurable)**

### **Command**
```bash
python -m n8n_builder.cli serve
```

### **Access URL**
- **http://localhost:8000** (default)

### **Features**
- ✅ **Configurable Host/Port** - Customize via command line options
- ✅ **Optional Auto-reload** - Enable with `--reload` flag
- ✅ **Production Ready** - Standard uvicorn configuration
- ✅ **CLI Integration** - Part of comprehensive CLI toolkit

### **Configuration Options**
```bash
# Basic usage
python -m n8n_builder.cli serve

# Custom port
python -m n8n_builder.cli serve --port 8080

# Custom host (allow external connections)
python -m n8n_builder.cli serve --host 0.0.0.0

# Enable auto-reload
python -m n8n_builder.cli serve --reload

# Combined options
python -m n8n_builder.cli serve --host 0.0.0.0 --port 8080 --reload
```

### **When to Use**
- ✅ **Production deployment** - Need specific host/port configuration
- ✅ **External access** - Serve to other machines on network
- ✅ **Custom ports** - Avoid conflicts with other services
- ✅ **CI/CD pipelines** - Scriptable with specific parameters

## 📊 **Comparison Table**

| Feature | Method A (`run.py`) | Method B (`cli serve`) |
|---------|-------------------|----------------------|
| **Port** | 8002 (fixed) | 8000 (configurable) |
| **Host** | 127.0.0.1 (fixed) | 127.0.0.1 (configurable) |
| **Auto-reload** | ✅ Always enabled | ⚙️ Optional (`--reload`) |
| **Port cleanup** | ✅ Automatic | ❌ Manual |
| **Error handling** | ✅ Enhanced | ⚙️ Standard |
| **Configuration** | ❌ Fixed | ✅ Flexible |
| **Development** | ✅ Optimized | ⚙️ Basic |
| **Production** | ⚙️ Limited | ✅ Suitable |

## 🎯 **Recommendations**

### **For Development (Recommended)**
```bash
python run.py
```
**Why**: Automatic port cleanup, enhanced error handling, and development-optimized configuration make this the most reliable choice for daily development work.

### **For Production or Custom Deployment**
```bash
python -m n8n_builder.cli serve --host 0.0.0.0 --port 8080
```
**Why**: Configurable options allow you to adapt to specific deployment requirements.

### **For Testing Different Configurations**
```bash
# Test on different port
python -m n8n_builder.cli serve --port 9000

# Test external access
python -m n8n_builder.cli serve --host 0.0.0.0
```

## 🔍 **Technical Details**

### **Method A Implementation (`run.py`)**
```python
# Key features from run.py:
def kill_processes_on_ports(ports: List[int]) -> None:
    """Kill any processes running on the specified ports."""
    # Comprehensive process cleanup logic

async def main():
    # Kill existing processes on ports 8002, 8080
    kill_processes_on_ports([8002, 8080])
    
    # Start server with enhanced configuration
    config = uvicorn.Config(
        "n8n_builder.app:app",
        host="127.0.0.1",
        port=8002,
        log_level="info",
        reload=True  # Always enabled
    )
```

### **Method B Implementation (`cli.py`)**
```python
# Key features from cli.py:
@cli.command()
@click.option('--host', default='127.0.0.1', help='Host to bind the server to')
@click.option('--port', default=8000, help='Port to bind the server to')
@click.option('--reload', is_flag=True, help='Enable auto-reload')
def serve(host: str, port: int, reload: bool):
    """Start the FastAPI server."""
    uvicorn.run("n8n_builder.app:app", host=host, port=port, reload=reload)
```

## 🚨 **Common Issues and Solutions**

### **Port Already in Use**
**Method A**: Automatically handles this with port cleanup
**Method B**: Manually kill processes or use different port:
```bash
# Find process using port
netstat -ano | findstr :8000
# Kill process by PID
taskkill /PID <PID> /F
# Or use different port
python -m n8n_builder.cli serve --port 8001
```

### **Can't Access from Other Machines**
**Method A**: Limited to localhost only
**Method B**: Use external host:
```bash
python -m n8n_builder.cli serve --host 0.0.0.0
```

### **Code Changes Not Reflecting**
**Method A**: Auto-reload always enabled
**Method B**: Add reload flag:
```bash
python -m n8n_builder.cli serve --reload
```

## 📝 **Documentation History**

**Issue Identified**: The original documentation only mentioned Method B (`cli serve`) but completely omitted Method A (`run.py`), which appears to be the more robust development option.

**Resolution**: Updated all documentation to include both methods with clear explanations of when to use each.

**Files Updated**:
- `README.md` - Added both methods to usage instructions
- `DOCUMENTATION_INDEX.md` - Updated quick start guide
- `Documentation/SERVER_STARTUP_METHODS.md` - This comprehensive guide

## 🎉 **Conclusion**

Both methods are valid and serve different purposes:

- **Use `python run.py`** for development and daily usage
- **Use `python -m n8n_builder.cli serve`** for production or custom configurations

The choice depends on your specific needs, but for most users, **Method A (`python run.py`)** provides the most reliable and hassle-free experience.

---

**💡 Pro Tip**: If you're unsure which method to use, start with `python run.py` - it's the most forgiving and handles common issues automatically!
