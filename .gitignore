# Logs
logs/n8n_builder.log
*.log

# Sensitive configuration files
.env
.env.local
.env.production
.env.development
config.ps1

# Personal/Local files
**/config.ps1
**/.env
**/.env.local

# IDE and OS files
.vscode/settings.json
.idea/
*.swp
*.swo
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*.tmp

# Windows-specific files
nul
CON
PRN
AUX

# nGrok configuration (contains auth tokens)
ngrok.yml
ngrok-config*.yml
**/ngrok.yml
**/ngrok-config*.yml
