# KnowledgeBase Dynamic Schema Information

**Location**: `Self_Healer/Documentation/DB_Admin/`  
**Created**: 2025-06-27  
**Purpose**: Dynamic schema retrieval system for Self-Healer KnowledgeBase integration

## 📋 Overview

This folder contains database administration tools for dynamically retrieving KnowledgeBase schema information. Instead of static schema documentation, this system provides real-time, up-to-date database structure information.

## 🔧 Database Administration Tools

### **1. Stored Procedure: `sp_GetKnowledgeBaseSchema`**

**File**: `create_schema_procedure.sql`  
**Purpose**: SQL Server stored procedure for dynamic schema retrieval

**Features**:
- **Dynamic table information** - Get all tables or specific table details
- **Complete column metadata** - Data types, constraints, relationships
- **Real-time row counts** - Current data volume information
- **Relationship mapping** - Foreign key relationships between tables
- **Flexible parameters** - Optional table name parameter

**Usage**:
```sql
-- Get all tables
EXEC sp_GetKnowledgeBaseSchema

-- Get specific table
EXEC sp_GetKnowledgeBaseSchema @TableName = 'REF_Entities'
```

### **2. Python Schema Retriever: `get_knowledgebase_schema.py`**

**File**: `get_knowledgebase_schema.py`  
**Purpose**: Python script that uses the stored procedure to retrieve and format schema information

**Features**:
- **Multiple output formats** - Text, JSON, Markdown
- **Command-line interface** - Easy to use from scripts or manually
- **Structured data processing** - Organized schema information
- **Error handling** - Robust database connection management

**Usage**:
```bash
# Get all tables (text format)
python get_knowledgebase_schema.py

# Get specific table
python get_knowledgebase_schema.py REF_Entities

# Get all tables as JSON
python get_knowledgebase_schema.py --format json

# Get specific table as Markdown
python get_knowledgebase_schema.py REF_Fact --format markdown
```

## 📊 Current KnowledgeBase Structure

**Database**: KnowledgeBase  
**Connection**: Uses MCP Database Tool with 'knowledgebase' connection

### **Available Tables**:
- **REF_Entities** - Entity definitions
- **REF_Fact** - Factual information with validity ratings
- **REF_Opinion** - Opinion data linked to facts
- **REF_Category** - Category classifications
- **REF_Attributes** - Attribute definitions
- **REF_EntityCategories** - Entity-category relationships
- **REF_EntityValues** - Entity attribute values
- **REF_Evidence** - Supporting evidence for facts/opinions

### **Key Column Mappings for Self-Healer Integration**:

| Self-Healer Expected | Actual KnowledgeBase Column |
|---------------------|----------------------------|
| `EntityID`          | `ID`                       |
| `EntityName`        | `Name`                     |
| `FactID`            | `ID`                       |
| `FactText`          | `Name`                     |
| `DateCreated`       | `CreateDate`               |

## 🎯 Integration Benefits

### **Dynamic Schema Awareness**
- **Always current** - Schema information is retrieved in real-time
- **No maintenance** - No need to update static documentation
- **Flexible queries** - Can get overview or detailed information as needed

### **Self-Healer Integration**
- **Column mapping** - Clear understanding of actual vs expected column names
- **Data validation** - Real-time row counts and data presence verification
- **Relationship understanding** - Foreign key relationships for proper data insertion

### **Development Efficiency**
- **Quick reference** - Fast access to current database structure
- **Multiple formats** - Choose the best format for your current need
- **Scriptable** - Can be integrated into automated processes

## 🚀 Usage Examples

### **For Development Reference**:
```bash
# Quick overview of all tables
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py

# Detailed view of entities table
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py REF_Entities
```

### **For Documentation Generation**:
```bash
# Generate markdown documentation
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py --format markdown > current_schema.md
```

### **For Automated Scripts**:
```bash
# Get JSON for programmatic processing
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py --format json > schema.json
```

## 📁 File Organization

```
Self_Healer/Documentation/DB_Admin/
├── KnowledgeBaseInfo.md              # This file - overview and usage
├── create_schema_procedure.sql       # SQL to create the stored procedure
└── get_knowledgebase_schema.py       # Python script to retrieve schema
```

## 🔄 Maintenance

### **To Update the Stored Procedure**:
1. Edit `create_schema_procedure.sql`
2. Execute the SQL script in SQL Server Management Studio or via MCP Database Tool
3. The procedure will be dropped and recreated with new functionality

### **To Enhance the Python Script**:
1. Edit `get_knowledgebase_schema.py`
2. Add new output formats, processing logic, or command-line options
3. Test with various table names and parameters

## 🎯 Future Enhancements

### **Planned Features**:
- **Schema comparison** - Compare current schema with previous versions
- **Data profiling** - Statistical analysis of table contents
- **Index information** - Performance optimization insights
- **Dependency mapping** - Complete relationship visualization

### **Integration Opportunities**:
- **Self-Healer auto-configuration** - Automatically adapt to schema changes
- **Monitoring dashboards** - Real-time schema and data health monitoring
- **Automated documentation** - Generate comprehensive database documentation

## 📞 Support

For questions about the KnowledgeBase schema or these tools:
1. **Check current schema**: Run `get_knowledgebase_schema.py` for latest information
2. **Review stored procedure**: Check `create_schema_procedure.sql` for query logic
3. **Test connections**: Verify MCP Database Tool connectivity to 'knowledgebase'
4. **Review naming conventions**: See [SQLConventions.md](../SQLConventions.md) for database standards

**Remember**: This system provides dynamic, real-time schema information - always use these tools for the most current database structure details!
