# Self-Healer Documentation Index

## 📚 Core Documentation

### 🏠 Main Documentation
- **[README.md](README.md)** - Complete system overview, installation, and usage guide
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Detailed technical architecture and SelfHealed flag system
- **[DesignPrincipals.md](DesignPrincipals.md)** - Core organizational and design principles for system development

### 🚀 Setup and Integration
- **[INTEGRATION_GUIDE.md](INTEGRATION_GUIDE.md)** - Integration with existing projects
- **[GENERIC_SETUP_GUIDE.md](GENERIC_SETUP_GUIDE.md)** - Generic setup for any project

### 🧠 KnowledgeBase Integration
- **[INTEGRATION_COMPLETE.md](INTEGRATION_COMPLETE.md)** - Complete KnowledgeBase integration summary
- **[SelfHealer_KnowledgeBase_Integration.md](SelfHealer_KnowledgeBase_Integration.md)** - Technical integration design
- **[KnowledgeBaseReadMe.md](KnowledgeBaseReadMe.md)** - KnowledgeBase system overview

### 🗄️ Database Administration
- **[DB_Admin/](DB_Admin/)** - Database administration tools and schema management
  - **[KnowledgeBaseInfo.md](DB_Admin/KnowledgeBaseInfo.md)** - Dynamic schema retrieval system overview
  - **[get_knowledgebase_schema.py](DB_Admin/get_knowledgebase_schema.py)** - Python schema retrieval tool
  - **[create_schema_procedure.sql](DB_Admin/create_schema_procedure.sql)** - SQL stored procedure for schema queries
  - **[install_and_test.py](DB_Admin/install_and_test.py)** - One-click setup and testing script

## 🔧 Key Features

### SelfHealed Flag System
The Self-Healer implements a comprehensive audit trail system:

#### ✅ Successful Healing
```
[2025-06-26 07:31:05,014] INFO self_healer.action: SelfHealed - Workflow Generation Error: Enhanced LLM prompt to require trigger nodes in all workflows
```

#### ❌ Failed Healing
```
[2025-06-26 07:31:15,234] INFO self_healer.action: SelfHealFailed - Database Connection Error: Healing failed: Connection timeout after retry attempts
```

### Benefits
- **📋 Complete Audit Trail**: Every healing action permanently recorded
- **🚫 Duplicate Prevention**: Already-healed errors automatically skipped
- **📊 Performance Visibility**: Easy to see effectiveness in logs
- **🔄 Resume Capability**: System knows where it left off after restarts
- **✅ Visual Confirmation**: Clear evidence Self-Healer is working

## 🛠️ Technical Details

### Error Detection
- **Simplified Patterns**: Reliable error detection using consistent log format
- **Module Extraction**: Separates module name and error message
- **Skip Already-Healed**: Prevents duplicate processing

### Monitoring Commands
```bash
# View recent healing activity
grep "SelfHealed\|SelfHealFailed" logs/n8n_builder.log | tail -20

# Count successful healings today
grep "SelfHealed" logs/n8n_builder.log | grep "$(date +%Y-%m-%d)" | wc -l

# Calculate success rate
total=$(grep -c "SelfHealed\|SelfHealFailed" logs/n8n_builder.log)
success=$(grep -c "SelfHealed" logs/n8n_builder.log)
echo "Success rate: $((success * 100 / total))%"
```

### API Endpoints
```bash
# Get healing statistics
curl http://localhost:8081/api/healing_stats

# Force rescan of recent logs
curl -X POST http://localhost:8081/api/rescan_logs

# View system status
curl http://localhost:8081/api/status
```

## 📁 File Structure

```
Self-Healer/
├── Documentation/           # 📚 All documentation (NEW LOCATION)
│   ├── README.md           # Main documentation
│   ├── ARCHITECTURE.md     # Technical architecture
│   └── INDEX.md           # This file
├── core/                   # 🔧 Core system components
│   ├── error_monitor.py    # Error detection with SelfHealed flags
│   ├── healer_manager.py   # Main orchestrator
│   ├── context_analyzer.py # Context gathering
│   ├── solution_generator.py # Solution creation
│   ├── solution_validator.py # Safety validation
│   └── learning_engine.py  # Continuous learning
├── config/                 # ⚙️ Configuration files
│   ├── healer_config.yaml  # Main configuration
│   └── project_config.yaml # Project-specific settings
├── dashboard/              # 🖥️ Web dashboard
│   └── dashboard.py        # Dashboard server
├── backups/               # 💾 Automatic backups
└── learning_data/         # 🧠 Learning history
```

## 🔗 Related Documentation

### N8N Builder Integration
- **Main Project**: [N8N_Builder README](../../README.md)
- **Documentation Index**: [Documentation/DOCUMENTATION_INDEX.md](../../Documentation/DOCUMENTATION_INDEX.md)

### Configuration
- **Healer Config**: [config/healer_config.yaml](../config/healer_config.yaml)
- **Project Config**: [config/project_config.yaml](../config/project_config.yaml)

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install watchdog pyyaml psutil
   ```

2. **Start Self-Healer**
   ```python
   from Self_Healer import SelfHealerManager
   healer = SelfHealerManager()
   await healer.start()
   ```

3. **Monitor Activity**
   ```bash
   # Watch healing actions in real-time
   tail -f logs/n8n_builder.log | grep "SelfHealed\|SelfHealFailed"
   
   # View dashboard
   open http://localhost:8081
   ```

## 📊 Monitoring Dashboard

Access the real-time dashboard at: **http://localhost:8081**

Features:
- 📈 Real-time healing activity
- 📊 Success rate metrics
- 🔍 Recent healing sessions
- ⚡ System health status
- 📋 Learning statistics

## 🆘 Troubleshooting

### Common Issues

1. **No errors detected**: Check log file paths and permissions
2. **Dashboard not accessible**: Verify port 8081 is available
3. **Healing not working**: Check LLM service and configuration
4. **High resource usage**: Adjust monitoring intervals

### Debug Commands

```bash
# Check Self-Healer logs
tail -f logs/n8n_builder.log | grep self_healer

# Verify configuration
python -c "import yaml; print(yaml.safe_load(open('Self-Healer/config/healer_config.yaml')))"

# Test error detection
grep "ERROR" logs/n8n_builder.log | head -5
```

## 📝 Recent Updates

### 🧠 KnowledgeBase Integration (Latest - December 2024)
- ✅ **Complete SQL Server Integration**: MCP database connection established
- ✅ **Validity Rating System**: Solutions rated based on effectiveness (0-100%)
- ✅ **Hyperlinked Audit Trail**: Clickable session IDs and error types
- ✅ **Knowledge Accumulation**: Every healing session builds institutional knowledge
- ✅ **API Endpoints**: RESTful access to knowledge data and analytics
- ✅ **Learning Engine**: Continuous improvement through accumulated evidence

### 🔧 SelfHealed Flag System
- ✅ Added comprehensive healing action logging
- ✅ Implemented duplicate error prevention
- ✅ Simplified error detection patterns
- ✅ Added log-based statistics extraction
- ✅ Created new API endpoints for monitoring

### 📁 Documentation Reorganization
- ✅ Consolidated all documentation in `Documentation/` folder
- ✅ Updated all internal links and cross-references
- ✅ Created comprehensive documentation index
- ✅ Enhanced architecture documentation with latest features
- ✅ Improved formatting and readability across all documents

## 🤝 Contributing

The Self-Healer system is designed to be extensible:

1. **Custom Solutions**: Add domain-specific healing patterns
2. **Enhanced Detection**: Improve error pattern recognition
3. **Additional Integrations**: Connect with external monitoring
4. **Advanced Analytics**: Enhance SelfHealed flag analysis

## 📄 License

Part of the N8N Builder project - same licensing terms apply.

---

**Need Help?** Check the troubleshooting section or review the detailed architecture documentation for technical details about the SelfHealed flag system and error detection improvements.
