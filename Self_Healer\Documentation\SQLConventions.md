# SQL Table and Stored Procedure Naming Conventions

**Created**: 2025-06-27  
**Purpose**: Standardized naming conventions for SQL Server tables, stored procedures, and database objects  
**Scope**: Applies to KnowledgeBase and all related database systems  
**Location**: `Self_Healer/Documentation/SQLConventions.md`

## 📋 Overview

This document defines the standardized naming conventions used throughout the KnowledgeBase and related database systems. These conventions ensure consistency, maintainability, and clear understanding of database structure and purpose.

## 🗂️ Table Naming Conventions

### **Reference Tables: `REF_<TableName>`**

**Purpose**: Base data tables containing core object definitions and properties.

**Convention**: 
- **Format**: `REF_<TableName>` (singular form)
- **Example**: `REF_Player`, `REF_Place`, `REF_Adventure`
- **NOT**: `REF_Players`, `REF_Places`, `REF_Adventures`

**Structure Pattern**:
```sql
REF_<TableName> (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- Auto-incrementing primary key
    Name            VARCHAR(255),                   -- Object name/title
    [ForeignKeyID]  INT,                           -- Foreign key references (if any)
    [Property1]     [DataType],                    -- Object-specific data fields
    [Property2]     [DataType],                    -- Additional properties
    CreateDate      DATETIME DEFAULT GETDATE()     -- Creation timestamp
)
```

**Examples**:
- `REF_Entities` - Entity definitions
- `REF_Fact` - Factual information with validity ratings
- `REF_Opinion` - Opinion data and assessments
- `REF_Category` - Category classifications

### **Cross-Reference Tables: `XRF_<TableName>`**

**Purpose**: Junction tables linking REF tables in many-to-many relationships.

**Convention**:
- **Format**: `XRF_<Table1>_<Table2>` or `XRF_<RelationshipName>`
- **Example**: `XRF_Place_Adventure`, `XRF_Player_Quest`

**Structure Pattern**:
```sql
XRF_<Relationship> (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- Auto-incrementing primary key
    Name            VARCHAR(255),                   -- Relationship name/description
    [Table1]ID      INT,                           -- Foreign key to first table
    [Table2]ID      INT,                           -- Foreign key to second table
    [Property1]     [DataType],                    -- Relationship-specific properties
    [Property2]     [DataType],                    -- Additional relationship data
    CreateDate      DATETIME DEFAULT GETDATE()     -- Creation timestamp
)
```

**Examples**:
- `XRF_Entity_Category` - Links entities to categories
- `XRF_Fact_Evidence` - Links facts to supporting evidence
- `XRF_Opinion_Source` - Links opinions to their sources

### **System Query Tables: `S_SYS_<QueryName>s`**

**Purpose**: Database-specific queries and system tables that should not be overwritten by automated tools.

**Convention**:
- **Format**: `S_SYS_<QueryName>s` (plural form for query results)
- **Example**: `S_SYS_UserSessions`, `S_SYS_ErrorLogs`
- **Note**: Any `_SYS_` tables are ignored by GDS (Generic Data System)

**Usage**:
- System monitoring and logging
- Database-specific administrative functions
- Custom queries that should persist through system updates

## 🔑 Field Naming Conventions

### **Primary Key Fields**

**Convention**: Always named `ID`
- **Format**: `ID`
- **Type**: `INT IDENTITY(1,1) PRIMARY KEY`
- **Position**: First field in every table

### **Name Fields**

**Convention**: Always named `Name`
- **Format**: `Name`
- **Type**: `VARCHAR(255)` or appropriate length
- **Position**: Second field in every table (after ID)
- **Purpose**: Human-readable identifier or title

### **Foreign Key Fields**

**Convention**: `<TableName>ID`
- **Format**: `<ReferencedTableName>ID`
- **Examples**: `PlayerID`, `PlaceID`, `AdventureID`, `EntityID`
- **Rule**: Only fields that are ID references should end with 'ID' (capitalized)
- **Position**: After Name field, before object-specific data fields

### **Data Fields**

**Convention**: Descriptive names without spaces
- **Format**: `PropertyName` or `Property_Name` (underscore for separation if needed)
- **Examples**: `Description`, `Geography`, `Character_Name`, `Validity_Rating`
- **Rule**: No spaces in field names; use underscore (_) as separator when needed

### **Standard System Fields**

**Convention**: Consistent system fields across all tables
- **CreateDate**: `DATETIME DEFAULT GETDATE()` - Record creation timestamp
- **ModifiedDate**: `DATETIME` - Last modification timestamp (optional)
- **IsActive**: `BIT DEFAULT 1` - Soft delete flag (optional)

## 📊 Table Structure Order

### **Standard Field Order**:
1. **ID** - Auto-incrementing primary key
2. **Name** - Human-readable identifier
3. **Foreign Key Fields** - References to other tables (e.g., `EntityID`, `CategoryID`)
4. **Object Data Fields** - Table-specific properties and data
5. **System Fields** - `CreateDate`, `ModifiedDate`, etc.

### **Example Table Structure**:
```sql
CREATE TABLE REF_Fact (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- 1. Primary key
    Name            VARCHAR(500),                   -- 2. Name/title
    EntityID        INT,                           -- 3. Foreign keys
    CategoryID      INT,
    FactText        TEXT,                          -- 4. Object data
    ValidityRating  DECIMAL(3,2),
    DataSource      VARCHAR(255),
    CreateDate      DATETIME DEFAULT GETDATE(),    -- 5. System fields
    ModifiedDate    DATETIME,
    IsActive        BIT DEFAULT 1
)
```

## 🔧 Stored Procedure Naming Conventions

The KnowledgeBase system uses a sophisticated stored procedure naming convention that supports automated CRUD generation while allowing for custom administrative and system procedures.

### **🤖 Auto-Generated CRUD Procedures**

**Purpose**: Standard Create, Read, Update, Delete operations automatically generated by utility tool.

**Convention**: `<ACTION>_<TABLE_TYPE>_<TABLE_NAME>[_P[_PARAMETER]]`

#### **Action Prefixes**:
- **`S_`** - SELECT operations (retrieve data)
- **`I_`** - INSERT operations (create new records)
- **`U_`** - UPDATE operations (modify existing records)
- **`D_`** - DELETE operations (remove records)
- **`QL_`** - QUERY LIST operations (specialized queries)

#### **Structure Examples**:
```sql
-- Basic CRUD operations
S_REF_Entities              -- Select all from REF_Entities
S_REF_Entities_P_ID         -- Select from REF_Entities by ID parameter
S_REF_Fact_DV               -- Select all REF_Fact data for DataView/grid display
I_REF_Fact                  -- Insert into REF_Fact
U_REF_Opinion_P_ID          -- Update REF_Opinion by ID parameter
D_XRF_CategoryFact_P_ID     -- Delete from XRF_CategoryFact by ID parameter

-- Parameterized procedures
S_REF_Evidence_P_FactID     -- Select Evidence by FactID parameter
S_XRF_CategoryFact_P_CategoryID  -- Select CategoryFact by CategoryID parameter
```

#### **Parameter Notation**:
- **`_P`** - Indicates procedure accepts parameters
- **`_P_<ParameterName>`** - Specific parameter (e.g., `_P_ID`, `_P_FactID`)
- **`_P_<Param1>_<Param2>`** - Multiple parameters listed in order (max 2 for clarity)
- **`_Prms`** - Multiple parameters (3+) - indicates developer should review procedure for parameter list
- **`_DV`** - DataView variant (returns all table data for grid display in ASP.Net)

#### **Parameter Complexity Examples**:
```sql
-- No parameters or single parameter
S_SYS_REF_Fact_Analysis                           -- Simple analysis, 0-1 parameters

-- Two parameters listed in procedure name (for user clarity)
S_SYS_XRF_EntityCategory_P_EntityID_CategoryID    -- Select by EntityID, then CategoryID
S_SYS_REF_Fact_P_Name_ValidityRating              -- Select by Name, then ValidityRating

-- Three or more parameters - use _Prms suffix
S_SYS_XRF_EntityRelationship_Prms                 -- Complex procedure with 3+ parameters
S_SYS_REF_AdvancedAnalysis_Prms                   -- Multiple parameters, review procedure
```

#### **Auto-Generation Process**:
1. **Utility scans database schema** - Analyzes table structures and relationships
2. **Deletes existing CRUD procedures** - Removes old auto-generated procedures
3. **Creates new CRUD procedures** - Generates fresh procedures based on current schema
4. **Preserves custom procedures** - Skips procedures with `_SYS_` in the name

### **🛡️ Protected System Procedures**

**Purpose**: Custom procedures that should not be deleted by the auto-generation utility.

**Convention**: `<ACTION>_SYS_<TABLE_TYPE>_<TABLE_NAME>_<PURPOSE>`

#### **Protection Mechanism**:
- **`_SYS_`** marker prevents deletion by auto-generation utility
- Allows custom business logic and specialized operations
- Maintains custom procedures through schema changes

#### **Examples**:
```sql
S_SYS_REF_Fact_ValidityAnalysis     -- Custom fact validity analysis
I_SYS_REF_Entity_BulkImport         -- Custom bulk import procedure
U_SYS_XRF_Category_Reorganize       -- Custom category reorganization
```

### **🔧 Administrative Procedures**

**Purpose**: Database administration, maintenance, and system management procedures.

**Convention**: `z_<ACTION>_SYS_Admin_<PURPOSE>`

#### **Design Principles**:
- **`z_` prefix** - Sorts to bottom of procedure list for easy grouping
- **`_SYS_Admin_`** - Clearly identifies administrative nature
- **Human-readable organization** - Easy to find and identify at a glance

#### **Example Administrative Procedures**:
```sql
z_S_SYS_Admin_KnowledgeBaseSchema   -- Schema information (our current procedure)
z_S_SYS_Admin_TableStatistics       -- Row counts and statistics
z_U_SYS_Admin_RebuildIndexes        -- Database maintenance
z_S_SYS_Admin_DataIntegrityCheck    -- Validation and diagnostics
```

### **📋 Naming Convention Summary**

| Procedure Type | Prefix Pattern | Protection | Purpose | Example |
|---------------|----------------|------------|---------|---------|
| **Auto CRUD** | `<ACTION>_<TABLE>[_P_<PARAM>]` | None | Standard operations | `S_REF_Entities_P_ID` |
| **Auto DataView** | `<ACTION>_<TABLE>_DV` | None | Grid display data | `S_REF_Fact_DV` |
| **Custom Business** | `<ACTION>_SYS_<TABLE>_<PURPOSE>` | Protected | Business logic | `S_SYS_REF_Fact_Analysis` |
| **Two Parameters** | `<ACTION>_SYS_<TABLE>_P_<P1>_<P2>` | Protected | Complex queries (2 params) | `S_SYS_XRF_Entity_P_Name_Category` |
| **Multi-Parameter** | `<ACTION>_SYS_<TABLE>_<PURPOSE>_Prms` | Protected | Complex queries (3+ params) | `S_SYS_XRF_EntityRelationship_Prms` |
| **Administrative** | `z_<ACTION>_SYS_Admin_<PURPOSE>` | Protected | DB administration | `z_S_SYS_Admin_Schema` |

### **🎯 Recommended Name for Our Schema Procedure**

Based on the actual KnowledgeBase conventions:

**`z_S_SYS_Admin_KnowledgeBaseSchema`**

**Breakdown**:
- **`z_`** - Administrative procedure (sorts to bottom)
- **`S_`** - SELECT operation (retrieving information)
- **`SYS_Admin_`** - System administrative procedure
- **`KnowledgeBaseSchema`** - Descriptive purpose

**Benefits**:
- ✅ **Protected from auto-generation** - Won't be deleted by CRUD utility
- ✅ **Clearly administrative** - Obvious purpose at a glance
- ✅ **Properly grouped** - Sorts with other admin procedures
- ✅ **Follows conventions** - Matches existing system patterns

## 📝 Stored Procedure Documentation Standards

### **Comment Header Convention**

All custom procedures (`_SYS_` and `z_*_SYS_Admin_*`) should use this standardized comment header:

```sql
/*
================================================
AUTHOR:    <Developer Name>
DATE:      <Creation Date>
PURPOSE:   <Clear description of business purpose>
NOTES:     <Special rules, performance notes, dependencies>
================================================
PARAMETERS: (For _Prms procedures)
   @Parameter1 DataType - Description and usage notes
   @Parameter2 DataType - Description (NULL for optional)

RETURNS:   <Description of return data/format>
================================================
USAGE:

   EXEC ProcedureName
        @Parameter1 = value1,
        @Parameter2 = value2

================================================
SUPPORT QUERIES:

   -- Helpful queries for debugging and data verification
   SELECT * FROM RelatedTable WHERE Condition

   -- Additional diagnostic queries as needed

================================================
*/
```

### **Documentation Requirements**

#### **Mandatory Documentation**:
- **All `_Prms` procedures** - Must include complete parameter descriptions
- **All `z_*_SYS_Admin_*` procedures** - Must include usage examples and support queries
- **Complex `_SYS_` procedures** - Should include notes about performance or dependencies

#### **Example: Multi-Parameter Procedure**:
```sql
/*
================================================
AUTHOR:    John Smith
DATE:      2025-06-27
PURPOSE:   Complex entity relationship analysis with filtering and scoring
NOTES:     Optimized for large datasets. Uses temp tables for intermediate
           results. @AnalysisType affects execution plan significantly.
================================================
PARAMETERS:
   @EntityID INT - Primary entity to analyze
   @CategoryID INT - Category filter (NULL for all categories)
   @DateFrom DATETIME - Analysis start date
   @DateTo DATETIME - Analysis end date
   @AnalysisType VARCHAR(50) - 'correlation', 'dependency', 'impact'
   @IncludeInactive BIT - Include inactive relationships (default 0)

RETURNS:   Analysis results with relationship scores and recommendations
================================================
USAGE:

   EXEC S_SYS_XRF_EntityRelationship_Prms
        @EntityID = 123,
        @CategoryID = 456,
        @DateFrom = '2024-01-01',
        @DateTo = '2024-12-31',
        @AnalysisType = 'correlation',
        @IncludeInactive = 0

================================================
SUPPORT QUERIES:

   -- Verify entity exists
   SELECT * FROM REF_Entities WHERE ID = 123

   -- Check category relationships
   SELECT * FROM XRF_EntityCategories WHERE EntityID = 123

   -- Review recent analysis history
   SELECT TOP 10 * FROM REF_AnalysisLog
   WHERE EntityID = 123 ORDER BY CreateDate DESC

================================================
*/
```

### **Documentation Benefits**

- **Immediate Understanding** - Everything needed is in the procedure header
- **Copy-Paste Usage** - No parameter guessing or trial-and-error
- **Debugging Support** - Support queries save investigation time
- **Knowledge Transfer** - New developers can use procedures immediately
- **Maintenance Tracking** - Clear ownership and modification history

## 🤖 CRUD Auto-Generation Utility

### **System Overview**

The KnowledgeBase system includes a powerful utility tool (created ~1999) that automatically generates all standard CRUD stored procedures based on database table schemas.

### **🔄 Auto-Generation Process**

#### **Workflow**:
1. **Schema Analysis** - Scans all tables and analyzes structure
2. **Procedure Cleanup** - Deletes existing auto-generated procedures
3. **CRUD Generation** - Creates fresh procedures for all CRUD operations
4. **Protection Respect** - Preserves procedures with `_SYS_` markers
5. **Validation** - Ensures all procedures are syntactically correct

#### **Trigger Events**:
- **Schema Changes** - Run after any table structure modifications
- **New Tables** - Automatically creates full CRUD suite for new tables
- **Column Updates** - Regenerates procedures when columns are added/modified
- **Relationship Changes** - Updates procedures when foreign keys change

### **🛡️ Protection & Benefits**

**Protection Mechanism**: `_SYS_` marker preserves custom procedures from auto-deletion

**Key Benefits**:
- **Automated Maintenance** - CRUD operations stay current with schema changes
- **Custom Preservation** - Business logic and admin procedures never lost
- **Consistent Quality** - All auto-generated procedures follow same patterns
- **Human Organization** - Prefixes clearly indicate purpose at a glance

**Current Distribution**: 119 total procedures (95% auto-generated, 5% custom/protected)



## 🎯 Quick Reference

### **Current KnowledgeBase Schema**:
- **8 REF Tables**: Entities, Fact, Opinion, Category, Attributes, Evidence, EntityCategories, EntityValues
- **119 Stored Procedures**: 95% auto-generated CRUD, 5% custom/protected
- **Self-Healer Mapping**: `EntityID`→`ID`, `EntityName`→`Name`, `DateCreated`→`CreateDate`

### **Implementation Checklist**:

#### **New Tables**:
1. Use `REF_` (reference) or `XRF_` (cross-reference) prefix
2. Singular names only (`REF_Entity` not `REF_Entities`)
3. Field order: ID, Name, Foreign Keys, Data Fields, System Fields
4. Run CRUD utility after creation

#### **Custom Procedures**:
1. **Business Logic**: `<ACTION>_SYS_<TABLE_TYPE>_<TABLE_NAME>_<PURPOSE>`
2. **Two Parameters**: `<ACTION>_SYS_<TABLE>_P_<Param1>_<Param2>`
3. **Multi-Parameter**: `<ACTION>_SYS_<TABLE>_<PURPOSE>_Prms` (3+ parameters)
4. **Administrative**: `z_<ACTION>_SYS_Admin_<PURPOSE>`
5. All patterns protected from auto-generation utility
6. Use standardized comment headers for documentation

#### **Schema Changes**:
1. Modify table structure following conventions
2. Run CRUD utility to regenerate standard procedures
3. Verify custom procedures are preserved
4. Update documentation

## 📞 Reference and Support

**For questions about SQL conventions**:
1. **This document** - Standard patterns and examples
2. **Existing KnowledgeBase tables** - Implementation examples
3. **z_S_SYS_Admin_KnowledgeBaseSchema** - Current schema procedure
4. **DesignPrincipals.md** - Overall organizational approach

**Key Principle**: These conventions ensure consistency, maintainability, and clear understanding across all database development efforts.
