# SQL Table and Stored Procedure Naming Conventions

**Created**: 2025-06-27  
**Purpose**: Standardized naming conventions for SQL Server tables, stored procedures, and database objects  
**Scope**: Applies to KnowledgeBase and all related database systems  
**Location**: `Self_Healer/Documentation/SQLConventions.md`

## 📋 Overview

This document defines the standardized naming conventions used throughout the KnowledgeBase and related database systems. These conventions ensure consistency, maintainability, and clear understanding of database structure and purpose.

## 🗂️ Table Naming Conventions

### **Reference Tables: `REF_<TableName>`**

**Purpose**: Base data tables containing core object definitions and properties.

**Convention**: 
- **Format**: `REF_<TableName>` (singular form)
- **Example**: `REF_Player`, `REF_Place`, `REF_Adventure`
- **NOT**: `REF_Players`, `REF_Places`, `REF_Adventures`

**Structure Pattern**:
```sql
REF_<TableName> (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- Auto-incrementing primary key
    Name            VARCHAR(255),                   -- Object name/title
    [ForeignKeyID]  INT,                           -- Foreign key references (if any)
    [Property1]     [DataType],                    -- Object-specific data fields
    [Property2]     [DataType],                    -- Additional properties
    CreateDate      DATETIME DEFAULT GETDATE()     -- Creation timestamp
)
```

**Examples**:
- `REF_Entities` - Entity definitions
- `REF_Fact` - Factual information with validity ratings
- `REF_Opinion` - Opinion data and assessments
- `REF_Category` - Category classifications

### **Cross-Reference Tables: `XRF_<TableName>`**

**Purpose**: Junction tables linking REF tables in many-to-many relationships.

**Convention**:
- **Format**: `XRF_<Table1>_<Table2>` or `XRF_<RelationshipName>`
- **Example**: `XRF_Place_Adventure`, `XRF_Player_Quest`

**Structure Pattern**:
```sql
XRF_<Relationship> (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- Auto-incrementing primary key
    Name            VARCHAR(255),                   -- Relationship name/description
    [Table1]ID      INT,                           -- Foreign key to first table
    [Table2]ID      INT,                           -- Foreign key to second table
    [Property1]     [DataType],                    -- Relationship-specific properties
    [Property2]     [DataType],                    -- Additional relationship data
    CreateDate      DATETIME DEFAULT GETDATE()     -- Creation timestamp
)
```

**Examples**:
- `XRF_Entity_Category` - Links entities to categories
- `XRF_Fact_Evidence` - Links facts to supporting evidence
- `XRF_Opinion_Source` - Links opinions to their sources

### **System Query Tables: `S_SYS_<QueryName>s`**

**Purpose**: Database-specific queries and system tables that should not be overwritten by automated tools.

**Convention**:
- **Format**: `S_SYS_<QueryName>s` (plural form for query results)
- **Example**: `S_SYS_UserSessions`, `S_SYS_ErrorLogs`
- **Note**: Any `_SYS_` tables are ignored by GDS (Generic Data System)

**Usage**:
- System monitoring and logging
- Database-specific administrative functions
- Custom queries that should persist through system updates

## 🔑 Field Naming Conventions

### **Primary Key Fields**

**Convention**: Always named `ID`
- **Format**: `ID`
- **Type**: `INT IDENTITY(1,1) PRIMARY KEY`
- **Position**: First field in every table

### **Name Fields**

**Convention**: Always named `Name`
- **Format**: `Name`
- **Type**: `VARCHAR(255)` or appropriate length
- **Position**: Second field in every table (after ID)
- **Purpose**: Human-readable identifier or title

### **Foreign Key Fields**

**Convention**: `<TableName>ID`
- **Format**: `<ReferencedTableName>ID`
- **Examples**: `PlayerID`, `PlaceID`, `AdventureID`, `EntityID`
- **Rule**: Only fields that are ID references should end with 'ID' (capitalized)
- **Position**: After Name field, before object-specific data fields

### **Data Fields**

**Convention**: Descriptive names without spaces
- **Format**: `PropertyName` or `Property_Name` (underscore for separation if needed)
- **Examples**: `Description`, `Geography`, `Character_Name`, `Validity_Rating`
- **Rule**: No spaces in field names; use underscore (_) as separator when needed

### **Standard System Fields**

**Convention**: Consistent system fields across all tables
- **CreateDate**: `DATETIME DEFAULT GETDATE()` - Record creation timestamp
- **ModifiedDate**: `DATETIME` - Last modification timestamp (optional)
- **IsActive**: `BIT DEFAULT 1` - Soft delete flag (optional)

## 📊 Table Structure Order

### **Standard Field Order**:
1. **ID** - Auto-incrementing primary key
2. **Name** - Human-readable identifier
3. **Foreign Key Fields** - References to other tables (e.g., `EntityID`, `CategoryID`)
4. **Object Data Fields** - Table-specific properties and data
5. **System Fields** - `CreateDate`, `ModifiedDate`, etc.

### **Example Table Structure**:
```sql
CREATE TABLE REF_Fact (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- 1. Primary key
    Name            VARCHAR(500),                   -- 2. Name/title
    EntityID        INT,                           -- 3. Foreign keys
    CategoryID      INT,
    FactText        TEXT,                          -- 4. Object data
    ValidityRating  DECIMAL(3,2),
    DataSource      VARCHAR(255),
    CreateDate      DATETIME DEFAULT GETDATE(),    -- 5. System fields
    ModifiedDate    DATETIME,
    IsActive        BIT DEFAULT 1
)
```

## 🔧 Stored Procedure Naming Conventions

The KnowledgeBase system uses a sophisticated stored procedure naming convention that supports automated CRUD generation while allowing for custom administrative and system procedures.

### **🤖 Auto-Generated CRUD Procedures**

**Purpose**: Standard Create, Read, Update, Delete operations automatically generated by utility tool.

**Convention**: `<ACTION>_<TABLE_TYPE>_<TABLE_NAME>[_P[_PARAMETER]]`

#### **Action Prefixes**:
- **`S_`** - SELECT operations (retrieve data)
- **`I_`** - INSERT operations (create new records)
- **`U_`** - UPDATE operations (modify existing records)
- **`D_`** - DELETE operations (remove records)
- **`QL_`** - QUERY LIST operations (specialized queries)

#### **Structure Examples**:
```sql
-- Basic CRUD operations
S_REF_Entities              -- Select all from REF_Entities
S_REF_Entities_P_ID         -- Select from REF_Entities by ID parameter
I_REF_Fact                  -- Insert into REF_Fact
I_REF_Fact_DV               -- Insert into REF_Fact with default values
U_REF_Opinion_P_ID          -- Update REF_Opinion by ID parameter
D_XRF_CategoryFact_P_ID     -- Delete from XRF_CategoryFact by ID parameter

-- Parameterized procedures
S_REF_Evidence_P_FactID     -- Select Evidence by FactID parameter
S_XRF_CategoryFact_P_CategoryID  -- Select CategoryFact by CategoryID parameter
```

#### **Parameter Notation**:
- **`_P`** - Indicates procedure accepts parameters
- **`_P_<ParameterName>`** - Specific parameter (e.g., `_P_ID`, `_P_FactID`)
- **`_DV`** - Default values variant for INSERT operations

#### **Auto-Generation Process**:
1. **Utility scans database schema** - Analyzes table structures and relationships
2. **Deletes existing CRUD procedures** - Removes old auto-generated procedures
3. **Creates new CRUD procedures** - Generates fresh procedures based on current schema
4. **Preserves custom procedures** - Skips procedures with `_SYS_` in the name

### **🛡️ Protected System Procedures**

**Purpose**: Custom procedures that should not be deleted by the auto-generation utility.

**Convention**: `<ACTION>_<TABLE_TYPE>_<TABLE_NAME>_SYS_<PURPOSE>`

#### **Protection Mechanism**:
- **`_SYS_`** marker prevents deletion by auto-generation utility
- Allows custom business logic and specialized operations
- Maintains custom procedures through schema changes

#### **Examples**:
```sql
S_REF_Fact_SYS_ValidityAnalysis     -- Custom fact validity analysis
I_REF_Entity_SYS_BulkImport         -- Custom bulk import procedure
U_XRF_Category_SYS_Reorganize       -- Custom category reorganization
```

### **🔧 Administrative Procedures**

**Purpose**: Database administration, maintenance, and system management procedures.

**Convention**: `z_<ACTION>_SYS_Admin_<PURPOSE>`

#### **Design Principles**:
- **`z_` prefix** - Sorts to bottom of procedure list for easy grouping
- **`_SYS_Admin_`** - Clearly identifies administrative nature
- **Human-readable organization** - Easy to find and identify at a glance

#### **Administrative Categories**:

##### **Schema and Metadata Procedures**:
```sql
z_S_SYS_Admin_KnowledgeBaseSchema   -- Retrieve complete schema information
z_S_SYS_Admin_TableStatistics       -- Get table row counts and statistics
z_S_SYS_Admin_RelationshipMap       -- Analyze table relationships
z_S_SYS_Admin_IndexAnalysis         -- Review index usage and performance
```

##### **Maintenance Procedures**:
```sql
z_U_SYS_Admin_RebuildIndexes        -- Rebuild database indexes
z_D_SYS_Admin_CleanupOrphans        -- Remove orphaned records
z_S_SYS_Admin_PerformanceReport     -- Generate performance analysis
z_U_SYS_Admin_UpdateStatistics      -- Update table statistics
```

##### **Monitoring and Diagnostics**:
```sql
z_S_SYS_Admin_ErrorLogAnalysis      -- Analyze system error patterns
z_S_SYS_Admin_UsageStatistics       -- Track procedure usage statistics
z_S_SYS_Admin_DataIntegrityCheck    -- Validate referential integrity
z_S_SYS_Admin_SecurityAudit         -- Review security and permissions
```

### **📋 Naming Convention Summary**

| Procedure Type | Prefix Pattern | Protection | Purpose | Example |
|---------------|----------------|------------|---------|---------|
| **Auto CRUD** | `<ACTION>_<TABLE>` | None | Standard operations | `S_REF_Entities_P_ID` |
| **Custom Business** | `<ACTION>_<TABLE>_SYS_` | Protected | Business logic | `S_REF_Fact_SYS_Analysis` |
| **Administrative** | `z_<ACTION>_SYS_Admin_` | Protected | DB administration | `z_S_SYS_Admin_Schema` |

### **🎯 Recommended Name for Our Schema Procedure**

Based on the actual KnowledgeBase conventions:

**`z_S_SYS_Admin_KnowledgeBaseSchema`**

**Breakdown**:
- **`z_`** - Administrative procedure (sorts to bottom)
- **`S_`** - SELECT operation (retrieving information)
- **`SYS_Admin_`** - System administrative procedure
- **`KnowledgeBaseSchema`** - Descriptive purpose

**Benefits**:
- ✅ **Protected from auto-generation** - Won't be deleted by CRUD utility
- ✅ **Clearly administrative** - Obvious purpose at a glance
- ✅ **Properly grouped** - Sorts with other admin procedures
- ✅ **Follows conventions** - Matches existing system patterns

## 🤖 CRUD Auto-Generation Utility

### **System Overview**

The KnowledgeBase system includes a powerful utility tool (created ~1999) that automatically generates all standard CRUD stored procedures based on database table schemas.

### **🔄 Auto-Generation Process**

#### **Workflow**:
1. **Schema Analysis** - Scans all tables and analyzes structure
2. **Procedure Cleanup** - Deletes existing auto-generated procedures
3. **CRUD Generation** - Creates fresh procedures for all CRUD operations
4. **Protection Respect** - Preserves procedures with `_SYS_` markers
5. **Validation** - Ensures all procedures are syntactically correct

#### **Trigger Events**:
- **Schema Changes** - Run after any table structure modifications
- **New Tables** - Automatically creates full CRUD suite for new tables
- **Column Updates** - Regenerates procedures when columns are added/modified
- **Relationship Changes** - Updates procedures when foreign keys change

### **🛡️ Protection Mechanisms**

#### **Custom Procedure Protection**:
- **`_SYS_` Marker** - Any procedure containing `_SYS_` is preserved
- **Administrative Procedures** - `z_*_SYS_Admin_*` procedures are protected
- **Business Logic** - Custom business procedures remain intact
- **Manual Procedures** - Hand-crafted procedures are not overwritten

#### **Benefits of Protection System**:
- **Schema Evolution** - Database can evolve without losing custom logic
- **Automated Maintenance** - CRUD operations stay current with schema
- **Custom Preservation** - Specialized procedures are never lost
- **Consistent Standards** - All auto-generated procedures follow conventions

### **🎯 Design Philosophy**

#### **Separation of Concerns**:
- **Auto-Generated** - Standard CRUD operations (predictable, consistent)
- **Custom Business** - Specialized logic (protected, preserved)
- **Administrative** - Database management (grouped, protected)

#### **Human-Readable Organization**:
- **Alphabetical Grouping** - Similar procedures sort together
- **Visual Identification** - Prefixes clearly indicate purpose
- **Administrative Grouping** - `z_` prefix groups admin procedures at bottom
- **At-a-Glance Understanding** - Naming reveals purpose immediately

#### **Maintenance Benefits**:
- **Always Current** - CRUD procedures match current schema
- **No Manual Sync** - Eliminates human error in procedure updates
- **Consistent Quality** - All auto-generated procedures follow same patterns
- **Reduced Maintenance** - Schema changes don't require manual procedure updates

### **📊 Procedure Distribution**

Based on current KnowledgeBase analysis:
- **119 Total Procedures** - All following the established conventions
- **Auto-Generated CRUD** - ~95% of procedures (S_, I_, U_, D_, QL_ prefixes)
- **Custom/Protected** - ~5% of procedures (containing _SYS_ markers)
- **Administrative** - Future procedures using `z_*_SYS_Admin_*` pattern

### **🔧 Implementation Guidelines**

#### **When Adding New Tables**:
1. **Create table** with proper naming conventions
2. **Run CRUD utility** to generate standard procedures
3. **Add custom procedures** with `_SYS_` protection if needed
4. **Create admin procedures** with `z_*_SYS_Admin_*` pattern if required

#### **When Modifying Schemas**:
1. **Make schema changes** following table conventions
2. **Run CRUD utility** to regenerate affected procedures
3. **Verify custom procedures** are preserved
4. **Test all functionality** to ensure compatibility

#### **When Creating Custom Procedures**:
1. **Use protection markers** - Include `_SYS_` in name
2. **Follow naming patterns** - Maintain consistency with conventions
3. **Document purpose** - Clear comments explaining custom logic
4. **Consider admin grouping** - Use `z_` prefix for administrative procedures

## 🎯 KnowledgeBase Specific Examples

### **Current KnowledgeBase Tables**:

```sql
-- Reference Tables (singular names)
REF_Entities        (ID, Name, CreateDate)
REF_Fact           (ID, Name, ValidityRating, DataSource, CreateDate)
REF_Opinion        (ID, Name, ValidityRating, FactID, Opinion, DataSource, CreateDate)
REF_Category       (ID, Name, Description, CreateDate)
REF_Attributes     (ID, Name, DataType, DefaultValue, CreateDate)

-- Cross-Reference Tables
XRF_EntityCategories   (ID, Name, EntityID, CategoryID, CreateDate)
XRF_EntityValues       (ID, Name, EntityID, AttributeID, Value, CreateDate)

-- Evidence and Supporting Data
REF_Evidence       (ID, Name, FactID, EvidenceType, EvidenceData, CreateDate)
```

### **Self-Healer Integration Mapping**:

| Self-Healer Expected | Actual KnowledgeBase | Convention Applied |
|---------------------|---------------------|-------------------|
| `EntityID`          | `ID`                | Primary key always `ID` |
| `EntityName`        | `Name`              | Name field always `Name` |
| `FactID`            | `ID`                | Primary key always `ID` |
| `FactText`          | `Name`              | Name field for content |
| `DateCreated`       | `CreateDate`        | Standard system field |

## 📝 Implementation Guidelines

### **When Creating New Tables**:

1. **Choose appropriate prefix**: `REF_` for reference data, `XRF_` for relationships
2. **Use singular names**: `REF_Entity` not `REF_Entities`
3. **Follow field order**: ID, Name, Foreign Keys, Data Fields, System Fields
4. **Apply naming rules**: No spaces, descriptive names, `ID` suffix only for foreign keys
5. **Include system fields**: At minimum `CreateDate`, consider `ModifiedDate` and `IsActive`

### **When Creating Stored Procedures**:

1. **Use descriptive prefixes**: `sp_Get`, `sp_Insert`, `sp_Update`, `sp_Delete`
2. **Include system name**: `sp_GetKnowledgeBaseSchema`
3. **Document parameters**: Clear parameter names and purposes
4. **Handle errors**: Comprehensive error handling and logging

### **When Modifying Existing Tables**:

1. **Maintain conventions**: Don't break existing naming patterns
2. **Add fields appropriately**: Follow field order guidelines
3. **Update procedures**: Ensure stored procedures reflect schema changes
4. **Document changes**: Update schema documentation and examples

## 🔄 Maintenance and Evolution

### **Convention Updates**:
- Document any changes to these conventions in this file
- Ensure backward compatibility when possible
- Communicate changes to all development team members
- Update existing documentation and examples

### **Compliance Checking**:
- Regular audits of database schema against these conventions
- Automated tools to validate naming compliance
- Code review processes that include convention verification
- Documentation updates when conventions evolve

## 📞 Reference and Support

For questions about SQL conventions:
1. **Review this document** for standard patterns and examples
2. **Check existing tables** in KnowledgeBase for implementation examples
3. **Consult stored procedures** like `sp_GetKnowledgeBaseSchema` for current structure
4. **Reference DesignPrincipals.md** for overall organizational approach

**Remember**: These conventions ensure consistency, maintainability, and clear understanding across all database development efforts. Following these standards makes the system more reliable and easier to work with for all developers.
