# SQL Table and Stored Procedure Naming Conventions

**Created**: 2025-06-27  
**Purpose**: Standardized naming conventions for SQL Server tables, stored procedures, and database objects  
**Scope**: Applies to KnowledgeBase and all related database systems  
**Location**: `Self_Healer/Documentation/SQLConventions.md`

## 📋 Overview

This document defines the standardized naming conventions used throughout the KnowledgeBase and related database systems. These conventions ensure consistency, maintainability, and clear understanding of database structure and purpose.

## 🗂️ Table Naming Conventions

### **Reference Tables: `REF_<TableName>`**

**Purpose**: Base data tables containing core object definitions and properties.

**Convention**: 
- **Format**: `REF_<TableName>` (singular form)
- **Example**: `REF_Player`, `REF_Place`, `REF_Adventure`
- **NOT**: `REF_Players`, `REF_Places`, `REF_Adventures`

**Structure Pattern**:
```sql
REF_<TableName> (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- Auto-incrementing primary key
    Name            VARCHAR(255),                   -- Object name/title
    [ForeignKeyID]  INT,                           -- Foreign key references (if any)
    [Property1]     [DataType],                    -- Object-specific data fields
    [Property2]     [DataType],                    -- Additional properties
    CreateDate      DATETIME DEFAULT GETDATE()     -- Creation timestamp
)
```

**Examples**:
- `REF_Entities` - Entity definitions
- `REF_Fact` - Factual information with validity ratings
- `REF_Opinion` - Opinion data and assessments
- `REF_Category` - Category classifications

### **Cross-Reference Tables: `XRF_<TableName>`**

**Purpose**: Junction tables linking REF tables in many-to-many relationships.

**Convention**:
- **Format**: `XRF_<Table1>_<Table2>` or `XRF_<RelationshipName>`
- **Example**: `XRF_Place_Adventure`, `XRF_Player_Quest`

**Structure Pattern**:
```sql
XRF_<Relationship> (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- Auto-incrementing primary key
    Name            VARCHAR(255),                   -- Relationship name/description
    [Table1]ID      INT,                           -- Foreign key to first table
    [Table2]ID      INT,                           -- Foreign key to second table
    [Property1]     [DataType],                    -- Relationship-specific properties
    [Property2]     [DataType],                    -- Additional relationship data
    CreateDate      DATETIME DEFAULT GETDATE()     -- Creation timestamp
)
```

**Examples**:
- `XRF_Entity_Category` - Links entities to categories
- `XRF_Fact_Evidence` - Links facts to supporting evidence
- `XRF_Opinion_Source` - Links opinions to their sources

### **System Query Tables: `S_SYS_<QueryName>s`**

**Purpose**: Database-specific queries and system tables that should not be overwritten by automated tools.

**Convention**:
- **Format**: `S_SYS_<QueryName>s` (plural form for query results)
- **Example**: `S_SYS_UserSessions`, `S_SYS_ErrorLogs`
- **Note**: Any `_SYS_` tables are ignored by GDS (Generic Data System)

**Usage**:
- System monitoring and logging
- Database-specific administrative functions
- Custom queries that should persist through system updates

## 🔑 Field Naming Conventions

### **Primary Key Fields**

**Convention**: Always named `ID`
- **Format**: `ID`
- **Type**: `INT IDENTITY(1,1) PRIMARY KEY`
- **Position**: First field in every table

### **Name Fields**

**Convention**: Always named `Name`
- **Format**: `Name`
- **Type**: `VARCHAR(255)` or appropriate length
- **Position**: Second field in every table (after ID)
- **Purpose**: Human-readable identifier or title

### **Foreign Key Fields**

**Convention**: `<TableName>ID`
- **Format**: `<ReferencedTableName>ID`
- **Examples**: `PlayerID`, `PlaceID`, `AdventureID`, `EntityID`
- **Rule**: Only fields that are ID references should end with 'ID' (capitalized)
- **Position**: After Name field, before object-specific data fields

### **Data Fields**

**Convention**: Descriptive names without spaces
- **Format**: `PropertyName` or `Property_Name` (underscore for separation if needed)
- **Examples**: `Description`, `Geography`, `Character_Name`, `Validity_Rating`
- **Rule**: No spaces in field names; use underscore (_) as separator when needed

### **Standard System Fields**

**Convention**: Consistent system fields across all tables
- **CreateDate**: `DATETIME DEFAULT GETDATE()` - Record creation timestamp
- **ModifiedDate**: `DATETIME` - Last modification timestamp (optional)
- **IsActive**: `BIT DEFAULT 1` - Soft delete flag (optional)

## 📊 Table Structure Order

### **Standard Field Order**:
1. **ID** - Auto-incrementing primary key
2. **Name** - Human-readable identifier
3. **Foreign Key Fields** - References to other tables (e.g., `EntityID`, `CategoryID`)
4. **Object Data Fields** - Table-specific properties and data
5. **System Fields** - `CreateDate`, `ModifiedDate`, etc.

### **Example Table Structure**:
```sql
CREATE TABLE REF_Fact (
    ID              INT IDENTITY(1,1) PRIMARY KEY,  -- 1. Primary key
    Name            VARCHAR(500),                   -- 2. Name/title
    EntityID        INT,                           -- 3. Foreign keys
    CategoryID      INT,
    FactText        TEXT,                          -- 4. Object data
    ValidityRating  DECIMAL(3,2),
    DataSource      VARCHAR(255),
    CreateDate      DATETIME DEFAULT GETDATE(),    -- 5. System fields
    ModifiedDate    DATETIME,
    IsActive        BIT DEFAULT 1
)
```

## 🔧 Stored Procedure Naming Conventions

### **Schema Information Procedures**

**Convention**: `sp_Get<SystemName><Purpose>`
- **Format**: `sp_Get<System><Information>`
- **Example**: `sp_GetKnowledgeBaseSchema`
- **Purpose**: Dynamic information retrieval

### **Data Manipulation Procedures**

**Convention**: `sp_<Action><TableName>`
- **Formats**: 
  - `sp_Insert<TableName>`
  - `sp_Update<TableName>`
  - `sp_Delete<TableName>`
  - `sp_Select<TableName>`
- **Examples**: `sp_InsertEntity`, `sp_UpdateFact`, `sp_SelectOpinions`

### **Business Logic Procedures**

**Convention**: `sp_<BusinessFunction>`
- **Examples**: `sp_ProcessSelfHealerAudit`, `sp_ValidateFactIntegrity`
- **Purpose**: Complex business operations and workflows

## 🎯 KnowledgeBase Specific Examples

### **Current KnowledgeBase Tables**:

```sql
-- Reference Tables (singular names)
REF_Entities        (ID, Name, CreateDate)
REF_Fact           (ID, Name, ValidityRating, DataSource, CreateDate)
REF_Opinion        (ID, Name, ValidityRating, FactID, Opinion, DataSource, CreateDate)
REF_Category       (ID, Name, Description, CreateDate)
REF_Attributes     (ID, Name, DataType, DefaultValue, CreateDate)

-- Cross-Reference Tables
XRF_EntityCategories   (ID, Name, EntityID, CategoryID, CreateDate)
XRF_EntityValues       (ID, Name, EntityID, AttributeID, Value, CreateDate)

-- Evidence and Supporting Data
REF_Evidence       (ID, Name, FactID, EvidenceType, EvidenceData, CreateDate)
```

### **Self-Healer Integration Mapping**:

| Self-Healer Expected | Actual KnowledgeBase | Convention Applied |
|---------------------|---------------------|-------------------|
| `EntityID`          | `ID`                | Primary key always `ID` |
| `EntityName`        | `Name`              | Name field always `Name` |
| `FactID`            | `ID`                | Primary key always `ID` |
| `FactText`          | `Name`              | Name field for content |
| `DateCreated`       | `CreateDate`        | Standard system field |

## 📝 Implementation Guidelines

### **When Creating New Tables**:

1. **Choose appropriate prefix**: `REF_` for reference data, `XRF_` for relationships
2. **Use singular names**: `REF_Entity` not `REF_Entities`
3. **Follow field order**: ID, Name, Foreign Keys, Data Fields, System Fields
4. **Apply naming rules**: No spaces, descriptive names, `ID` suffix only for foreign keys
5. **Include system fields**: At minimum `CreateDate`, consider `ModifiedDate` and `IsActive`

### **When Creating Stored Procedures**:

1. **Use descriptive prefixes**: `sp_Get`, `sp_Insert`, `sp_Update`, `sp_Delete`
2. **Include system name**: `sp_GetKnowledgeBaseSchema`
3. **Document parameters**: Clear parameter names and purposes
4. **Handle errors**: Comprehensive error handling and logging

### **When Modifying Existing Tables**:

1. **Maintain conventions**: Don't break existing naming patterns
2. **Add fields appropriately**: Follow field order guidelines
3. **Update procedures**: Ensure stored procedures reflect schema changes
4. **Document changes**: Update schema documentation and examples

## 🔄 Maintenance and Evolution

### **Convention Updates**:
- Document any changes to these conventions in this file
- Ensure backward compatibility when possible
- Communicate changes to all development team members
- Update existing documentation and examples

### **Compliance Checking**:
- Regular audits of database schema against these conventions
- Automated tools to validate naming compliance
- Code review processes that include convention verification
- Documentation updates when conventions evolve

## 📞 Reference and Support

For questions about SQL conventions:
1. **Review this document** for standard patterns and examples
2. **Check existing tables** in KnowledgeBase for implementation examples
3. **Consult stored procedures** like `sp_GetKnowledgeBaseSchema` for current structure
4. **Reference DesignPrincipals.md** for overall organizational approach

**Remember**: These conventions ensure consistency, maintainability, and clear understanding across all database development efforts. Following these standards makes the system more reliable and easier to work with for all developers.
