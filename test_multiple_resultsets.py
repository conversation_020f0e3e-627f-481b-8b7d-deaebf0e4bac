#!/usr/bin/env python3
"""
Test the z_S_SYS_Admin_KnowledgeBaseSchema procedure with multiple result sets
"""
import asyncio
import sys
sys.path.insert(0, '.')
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def test_multiple_resultsets():
    db_tool = MCPDatabaseTool('knowledgebase')
    
    print('🔍 Testing z_S_SYS_Admin_KnowledgeBaseSchema with MULTIPLE RESULT SETS...')
    
    try:
        # Test the procedure with multiple result sets
        print('\n=== Testing procedure with multiple result sets ===')
        result = await db_tool.execute_query('EXEC z_S_SYS_Admin_KnowledgeBaseSchema')
        
        status = result.get('status')
        rows = result.get('rows', [])
        
        print(f'Status: {status}')
        print(f'Total rows returned: {len(rows)}')
        
        if status == 'success' and rows:
            print('\n📊 ANALYZING RESULT SETS:')
            
            # Group rows by QueryType to see what result sets we got
            query_types = {}
            for row in rows:
                query_type = row.get('QueryType', 'UNKNOWN')
                if query_type not in query_types:
                    query_types[query_type] = []
                query_types[query_type].append(row)
            
            print(f'\n🔍 Found {len(query_types)} different QueryTypes:')
            for query_type, type_rows in query_types.items():
                print(f'   - {query_type}: {len(type_rows)} rows')
            
            # Show sample data from each result set
            for query_type, type_rows in query_types.items():
                print(f'\n📋 Sample data from {query_type}:')
                for i, row in enumerate(type_rows[:3]):  # Show first 3 rows of each type
                    print(f'   Row {i+1}: {row}')
                if len(type_rows) > 3:
                    print(f'   ... and {len(type_rows) - 3} more rows')
            
            # Check if we got all expected result sets
            expected_types = ['ALL_TABLES', 'RELATIONSHIPS', 'ALL_ROW_COUNTS']
            missing_types = [t for t in expected_types if t not in query_types.keys()]
            
            if missing_types:
                print(f'\n⚠️ Missing expected result sets: {missing_types}')
                print('   This suggests the MCP tool may only be reading the first result set')
            else:
                print(f'\n✅ SUCCESS! Got all expected result sets: {list(query_types.keys())}')
                
        elif status == 'success' and not rows:
            print('\n⚠️ Procedure executed successfully but returned 0 rows')
        else:
            error = result.get('error')
            print(f'\n❌ Error: {error}')
            
        # Test with specific table parameter
        print('\n=== Testing with REF_Entities parameter (should return multiple result sets) ===')
        result = await db_tool.execute_query("EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = 'REF_Entities'")
        
        status = result.get('status')
        rows = result.get('rows', [])
        
        print(f'Status: {status}')
        print(f'Total rows returned: {len(rows)}')
        
        if status == 'success' and rows:
            # Group by QueryType for parameter test
            query_types = {}
            for row in rows:
                query_type = row.get('QueryType', 'UNKNOWN')
                if query_type not in query_types:
                    query_types[query_type] = []
                query_types[query_type].append(row)
            
            print(f'\n🔍 Parameter test - Found {len(query_types)} QueryTypes:')
            for query_type, type_rows in query_types.items():
                print(f'   - {query_type}: {len(type_rows)} rows')
                
            # Show sample from each type
            for query_type, type_rows in query_types.items():
                print(f'\n📋 {query_type} sample:')
                for i, row in enumerate(type_rows[:2]):  # Show first 2 rows
                    print(f'   Row {i+1}: {row}')
                    
            # Check for expected single-table result sets
            expected_single_types = ['TABLE_INFO', 'COLUMN_INFO', 'ROW_COUNT']
            found_single_types = [t for t in expected_single_types if t in query_types.keys()]
            
            if found_single_types:
                print(f'\n✅ Single table test SUCCESS! Found: {found_single_types}')
            else:
                print(f'\n⚠️ Expected single table result sets not found')
                print(f'   Expected: {expected_single_types}')
                print(f'   Found: {list(query_types.keys())}')
        else:
            print('\n⚠️ Parameter test returned no data')
            
    except Exception as e:
        print(f'Exception: {e}')
        import traceback
        print(f'Traceback: {traceback.format_exc()}')

if __name__ == "__main__":
    asyncio.run(test_multiple_resultsets())
