# Self-Healer System

> **📚 All documentation is organized in [Documentation/](Documentation/INDEX.md)**

A comprehensive self-healing and self-improving system for the N8N Builder workflow automation platform. The Self-Healer automatically detects, analyzes, and resolves issues while learning from each healing attempt to improve future performance.

## 🚀 Quick Links

- **📖 [Complete Documentation](Documentation/README.md)** - Full system guide with SelfHealed flag system
- **🏗️ [Architecture Guide](Documentation/ARCHITECTURE.md)** - Technical implementation details
- **📋 [Documentation Index](Documentation/INDEX.md)** - All documentation organized
- **🧠 [KnowledgeBase Integration](Documentation/INTEGRATION_COMPLETE.md)** - Advanced audit trail system
- **⚙️ [Setup Guides](Documentation/GENERIC_SETUP_GUIDE.md)** - Installation and configuration

## 🔧 SelfHealed Flag System (Latest Feature)

The Self-Healer now implements a comprehensive audit trail system:

```bash
# View recent healing activity
grep "SelfHealed\|SelfHealFailed" logs/n8n_builder.log | tail -10

# Monitor dashboard
open http://localhost:8081
```

**Benefits:**
- ✅ Complete audit trail of all healing actions
- 🚫 Prevents duplicate processing of resolved errors
- 📊 Easy performance visibility in logs
- 🔄 Resume capability after system restarts

## Overview

The Self-Healer system provides:

- **Automatic Error Detection**: Monitors log files and system state for issues using simplified, reliable patterns
- **Intelligent Analysis**: Gathers comprehensive context using documentation and code analysis
- **Solution Generation**: Creates targeted solutions using local LLM and pattern recognition
- **Safe Implementation**: Validates and safely implements solutions with rollback capabilities
- **Continuous Learning**: Learns from successful and failed healing attempts
- **Audit Trail**: Logs all healing actions with "SelfHealed" flags for complete tracking and transparency

## Architecture

### Core Components

1. **Error Monitor** (`core/error_monitor.py`)
   - Real-time log file monitoring with simplified, reliable error patterns
   - Error classification and deduplication
   - Pattern recognition for recurring issues
   - SelfHealed flag tracking to prevent duplicate processing
   - Healing action logging for complete audit trail

2. **Context Analyzer** (`core/context_analyzer.py`)
   - Documentation retrieval from structured docs
   - Code context gathering using codebase analysis
   - System state assessment and dependency mapping

3. **Solution Generator** (`core/solution_generator.py`)
   - Local LLM integration for intelligent solutions
   - Pattern-based solutions for common errors
   - Multi-strategy approach with risk assessment

4. **Solution Validator** (`core/solution_validator.py`)
   - Comprehensive safety validation
   - Isolated testing environment
   - Automatic rollback capabilities

5. **Learning Engine** (`core/learning_engine.py`)
   - Pattern recognition and learning
   - Success/failure analysis
   - Continuous improvement insights

6. **Healer Manager** (`core/healer_manager.py`)
   - Main orchestrator coordinating all components
   - Session management and monitoring
   - Metrics collection and reporting

## Installation

### Prerequisites

- Python 3.8+
- N8N Builder system
- Local LLM service (LM Studio with mimo-vl-7b-rl model)
- Required Python packages (see requirements.txt)

### Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   pip install watchdog pyyaml psutil
   ```

2. **Configure System**
   ```bash
   # Copy and customize configuration
   cp Self-Healer/config/healer_config.yaml.template Self-Healer/config/healer_config.yaml
   # Edit configuration as needed
   ```

3. **Initialize Directories**
   ```bash
   mkdir -p Self-Healer/backups
   mkdir -p Self-Healer/learning_data
   ```

## Usage

### Starting the Self-Healer

```python
from Self_Healer import SelfHealerManager

# Initialize and start the self-healer
healer = SelfHealerManager()
await healer.start()

# The system will now automatically monitor and heal issues
```

### Manual Healing Session

```python
# Trigger manual healing for a specific error
error_detail = ErrorDetail(...)
context = await healer.context_analyzer.analyze_error(error_detail)
solutions = await healer.solution_generator.generate_solutions(error_detail, context)

# Validate and implement best solution
best_solution = solutions[0]
validation = await healer.solution_validator.validate_solution(best_solution, context)

if validation['can_proceed']:
    result = await healer.solution_validator.implement_solution(best_solution, context)
```

### Monitoring Status

```python
# Get system status
status = await healer.get_status()
print(f"Status: {status['status']}")
print(f"Active Sessions: {status['active_sessions']}")
print(f"Success Rate: {status['metrics']['success_rate']:.1f}%")

# Get learning statistics
learning_stats = healer.learning_engine.get_learning_statistics()
print(f"Total Patterns: {learning_stats['total_patterns']}")
print(f"Success Rate: {learning_stats['overall_success_rate']:.1f}%")
```

## Configuration

The system is configured through `config/healer_config.yaml`:

### Key Configuration Sections

- **Monitoring**: Error detection and session management
- **Safety**: Validation requirements and safety limits
- **Learning**: Pattern recognition and improvement settings
- **Validation**: Solution testing and verification
- **Integration**: Integration with existing N8N Builder components

### Safety Features

- **Rate Limiting**: Maximum healing attempts per hour
- **Validation Requirements**: Mandatory solution validation
- **Automatic Rollback**: Rollback on implementation failure
- **Emergency Stop**: Automatic shutdown on consecutive failures
- **File Change Limits**: Maximum files that can be modified

## Integration with N8N Builder

The Self-Healer integrates seamlessly with existing N8N Builder components:

- **Error Handler**: Extends existing error categorization
- **Logging System**: Uses existing logging infrastructure
- **Project Manager**: Coordinates with file operations
- **Performance Optimizer**: Integrates with performance monitoring
- **Documentation**: Leverages structured documentation for context

## SelfHealed Flag System

### Healing Action Tracking

The Self-Healer implements a comprehensive audit trail system that logs all healing actions directly to the main log file:

#### Successful Healing
```
[2025-06-26 07:31:05,014] INFO self_healer.action: SelfHealed - Workflow Generation Error: Enhanced LLM prompt to require trigger nodes in all workflows
```

#### Failed Healing
```
[2025-06-26 07:31:15,234] INFO self_healer.action: SelfHealFailed - Database Connection Error: Healing failed: Connection timeout after retry attempts
```

### Benefits

- **Complete Audit Trail**: Every healing action is permanently recorded with timestamp and one-sentence summary
- **Duplicate Prevention**: Already-healed errors are automatically skipped on system restart
- **Performance Visibility**: Easy to see Self-Healer effectiveness by searching logs for "SelfHealed" entries
- **Resume Capability**: System knows exactly where it left off after restarts
- **Visual Confirmation**: Clear evidence in logs that Self-Healer is actively working

### Error Detection Improvements

The system now uses simplified, reliable error patterns that match the consistent log format:

```python
# Simple pattern that catches all ERROR entries
'error_log_entry': re.compile(r'\[.*?\]\s+ERROR\s+([^:]+):\s*(.+)', re.IGNORECASE)
```

This approach:
- **Catches all errors** regardless of specific content
- **Extracts module name** and error message separately
- **Avoids complex regex** that might miss edge cases
- **Provides consistent parsing** across all error types

## Learning and Improvement

### Pattern Recognition

The system learns patterns from:
- Error types and their successful solutions
- Context factors that influence solution effectiveness
- System states that predict certain error types
- Solution approaches that work best for specific scenarios
- Historical healing success rates from log analysis

### Continuous Improvement

- **Success Tracking**: Records effectiveness of all solutions with permanent log entries
- **Pattern Evolution**: Updates patterns based on new data and healing outcomes
- **Insight Generation**: Provides actionable insights for system improvement
- **Predictive Capabilities**: Develops proactive healing strategies
- **Log-Based Metrics**: Analyzes healing statistics directly from log file entries

## Safety and Reliability

### Safety Measures

- **Comprehensive Validation**: Multi-layer solution validation
- **Rollback Points**: Automatic backup before changes
- **Impact Assessment**: Analysis of potential side effects
- **Gradual Implementation**: Phased deployment with monitoring
- **Emergency Controls**: Manual override and emergency stop

### Reliability Features

- **Error Recovery**: Graceful handling of system errors
- **State Persistence**: Maintains state across restarts
- **Resource Management**: Efficient memory and CPU usage
- **Monitoring**: Comprehensive system health monitoring

## Monitoring and Metrics

### Key Metrics

- **Error Detection Rate**: Errors detected vs. actual errors
- **Solution Success Rate**: Successful implementations vs. attempts (tracked via SelfHealed flags)
- **Healing Time**: Average time from detection to resolution
- **System Stability**: Overall system health improvements
- **Learning Effectiveness**: Pattern recognition accuracy
- **Healing Activity**: Frequency and success of healing actions visible in logs

### Monitoring SelfHealed Flags

#### View Recent Healing Activity
```bash
# See all healing actions in the last 24 hours
grep "SelfHealed\|SelfHealFailed" logs/n8n_builder.log | tail -20

# Count successful healings today
grep "SelfHealed" logs/n8n_builder.log | grep "$(date +%Y-%m-%d)" | wc -l

# View healing success rate
grep -c "SelfHealed" logs/n8n_builder.log
grep -c "SelfHealFailed" logs/n8n_builder.log
```

#### API Endpoints
```bash
# Get healing statistics from log analysis
curl http://localhost:8081/api/healing_stats

# Force rescan of recent log entries
curl -X POST http://localhost:8081/api/rescan_logs
```

### Dashboard (Future Enhancement)

- Real-time healing activity monitor
- Historical trends and patterns
- System health overview
- Performance impact assessment

## Troubleshooting

### Common Issues

1. **LLM Connection Failures**
   - Verify LM Studio is running
   - Check model is loaded (mimo-vl-7b-rl)
   - Validate endpoint configuration

2. **Permission Errors**
   - Ensure proper file permissions
   - Check backup directory access
   - Verify log file write permissions

3. **High Memory Usage**
   - Adjust `max_records_in_memory` setting
   - Enable record archiving
   - Monitor learning data size

### Debugging

Enable debug logging:
```yaml
logging:
  level: "DEBUG"
```

Check system logs:
```bash
tail -f logs/n8n_builder.log | grep self_healer
```

## Future Enhancements

### Planned Features

- **Distributed Healing**: Multi-instance coordination
- **Advanced ML Models**: Enhanced pattern recognition
- **External Integration**: Integration with monitoring systems
- **Automated Documentation**: Self-updating documentation
- **Predictive Maintenance**: Proactive issue prevention

### Contributing

The Self-Healer system is designed to be extensible:

1. **Custom Solution Templates**: Add domain-specific solutions
2. **Enhanced Pattern Recognition**: Improve learning algorithms
3. **Additional Integrations**: Connect with external systems
4. **Advanced Validation**: Implement specialized validators

## License

This Self-Healer system is part of the N8N Builder project and follows the same licensing terms.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review system logs for error details
3. Consult the N8N Builder documentation
4. Report issues through the project's issue tracking system
