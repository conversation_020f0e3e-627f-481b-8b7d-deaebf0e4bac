# Context Management Guide for N8N Builder

## Overview

This guide establishes a systematic approach for maintaining context and accessing key information when working on the N8N Builder project, especially during extended development sessions or when switching between different aspects of the system.

## Key Information Sources

### 1. **Auto-Generated Technical Documentation**
- **Location**: `Documentation/technical/ProcessFlow.md`
- **Generated by**: `Scripts/generate_process_flow.py`
- **Purpose**: Provides comprehensive system architecture overview
- **When to use**: 
  - Before making systemic changes
  - When needing to understand module relationships
  - When context is lost during long development sessions
  - When analyzing impact of changes across the system

### 2. **Project Memories**
- **Tool**: `remember` function stores key insights and patterns
- **Purpose**: Persistent knowledge across chat sessions
- **Content**: Project requirements, user preferences, architectural decisions

### 3. **Configuration and Setup Documentation**
- **Location**: `Documentation/` folder
- **Key files**:
  - `GITHUB_API_SETUP.md` - GitHub API authentication
  - `CREDENTIALS_SETUP.md` - Service credentials
  - `RunSystem.md` - System operation instructions

## Standard Context Recovery Workflow

When starting work or when context is needed:

### Step 1: Review Current System State
```bash
# Update technical documentation
python Scripts/generate_process_flow.py

# Check system status
python run.py  # (if not already running)
```

### Step 2: Access Key Documentation
1. **Review ProcessFlow.md** for system architecture
2. **Check memories** for project-specific requirements
3. **Review relevant setup documentation** for the area being worked on

### Step 3: Understand Current Issues
1. **Check logs** for current system behavior
2. **Review recent changes** in git history if needed
3. **Identify dependencies** using ProcessFlow.md

## Information Hierarchy

### **Level 1: Critical System Information**
- System architecture (ProcessFlow.md)
- Core configuration (config.py)
- Main application flow (n8n_builder.py, app.py)

### **Level 2: Feature-Specific Information**
- MCP Research Tool (mcp_research_tool.py, enhanced_prompt_builder.py)
- Validation system (validators.py, validation/)
- Error handling (error_handler.py)
- Performance optimization (performance_optimizer.py)

### **Level 3: Supporting Information**
- Testing frameworks (tests/)
- Documentation (Documentation/)
- Scripts and utilities (Scripts/)

## Context Questions to Ask

When working on any feature, systematically ask:

1. **Architecture**: How does this component fit in the overall system?
2. **Dependencies**: What other modules does this depend on?
3. **Impact**: What other parts of the system might be affected?
4. **Configuration**: What configuration options are relevant?
5. **Testing**: What tests exist and need to be updated?
6. **Documentation**: What documentation needs to be updated?

## Automated Context Tools

### **ProcessFlow Generator**
- **Command**: `python Scripts/generate_process_flow.py`
- **Frequency**: After significant changes
- **Output**: Updated system architecture documentation

### **Memory System**
- **Tool**: `remember` function
- **Use for**: Persistent insights, user preferences, architectural decisions
- **Review**: Check memories at start of each session

### **Codebase Retrieval**
- **Tool**: `codebase-retrieval` function
- **Use for**: Understanding specific code sections
- **Best practice**: Ask for comprehensive information in single calls

## Context Maintenance Best Practices

### **During Development**
1. **Update ProcessFlow.md** after structural changes
2. **Document decisions** using the remember tool
3. **Maintain clear commit messages** for future reference
4. **Update relevant documentation** as changes are made

### **Between Sessions**
1. **Save key insights** to memories
2. **Update documentation** for completed features
3. **Note any pending issues** or next steps
4. **Ensure ProcessFlow.md** reflects current state

### **When Switching Focus Areas**
1. **Review ProcessFlow.md** for the new area
2. **Check dependencies** and related modules
3. **Review existing tests** for the area
4. **Understand configuration** requirements

## Quick Reference Commands

```bash
# Update system documentation
python Scripts/generate_process_flow.py

# Start system
python run.py

# Run tests for specific area
python -m pytest tests/test_[area].py

# Check system health
curl http://localhost:8002/llm/health
```

## Integration with Development Workflow

### **Before Starting Work**
1. Review memories for project context
2. Update and review ProcessFlow.md
3. Check current system status
4. Identify relevant documentation

### **During Work**
1. Use codebase-retrieval for detailed code understanding
2. Update documentation as changes are made
3. Test changes incrementally
4. Document decisions and insights

### **After Completing Work**
1. Update ProcessFlow.md if structural changes were made
2. Save key insights to memories
3. Update relevant documentation
4. Ensure tests pass and system is stable

## Memory Categories

### **Project Requirements**
- User preferences and priorities
- System architecture decisions
- Integration requirements

### **Technical Insights**
- Performance considerations
- Security requirements
- Error handling patterns

### **Development Patterns**
- Code organization principles
- Testing strategies
- Documentation standards

This context management system ensures that important information is preserved and easily accessible, reducing the need to rediscover information and maintaining development efficiency across sessions.
