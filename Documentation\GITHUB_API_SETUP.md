# GitHub API Setup for N8N Builder

## Overview

The N8N Builder's MCP Research Tool can use GitHub API authentication to search for n8n workflow examples and documentation. Without authentication, GitHub API requests are rate-limited to 60 requests per hour. With authentication, this increases to 5,000 requests per hour.

## Setting Up GitHub API Token

### 1. Create a GitHub Personal Access Token

1. Go to GitHub Settings: https://github.com/settings/tokens
2. Click "Generate new token" → "Generate new token (classic)"
3. Give your token a descriptive name (e.g., "N8N Builder Research")
4. Set expiration as needed (recommended: 90 days or no expiration for development)
5. Select scopes:
   - **public_repo** (to read public repositories)
   - **repo** (if you need access to private repositories)
6. Click "Generate token"
7. **IMPORTANT**: Copy the token immediately - you won't be able to see it again!

### 2. Configure the Environment Variable

#### Option A: Set Environment Variable (Recommended)
```bash
# Windows (PowerShell)
$env:GITHUB_API_TOKEN="your_github_token_here"

# Windows (Command Prompt)
set GITHUB_API_TOKEN=your_github_token_here

# Linux/Mac
export GITHUB_API_TOKEN="your_github_token_here"
```

#### Option B: Add to System Environment Variables (Windows)
1. Open System Properties → Advanced → Environment Variables
2. Add new User or System variable:
   - Name: `GITHUB_API_TOKEN`
   - Value: `your_github_token_here`
3. Restart your terminal/IDE

### 3. Verify Configuration

After setting the environment variable, restart N8N Builder and check the logs:

```
[INFO] GitHub API authentication configured
```

If you see this message, authentication is working. If you see:
```
[WARNING] No GitHub API token provided - using unauthenticated requests (rate limited)
```

Then the environment variable is not set correctly.

## Testing GitHub API Access

You can test the GitHub API integration by generating a workflow that would trigger research:

1. Start N8N Builder: `python run.py`
2. Open http://localhost:8002
3. Try generating a workflow with a description like:
   - "Create a workflow that processes GitHub webhooks"
   - "Build an automation that monitors GitHub repositories"

Check the logs for GitHub API requests and responses.

## Rate Limits

- **Without authentication**: 60 requests/hour per IP
- **With authentication**: 5,000 requests/hour per token
- **Search API**: 30 requests/minute (authenticated)

## Security Notes

- Keep your GitHub token secure and never commit it to version control
- Use environment variables or secure credential storage
- Consider using a token with minimal required permissions
- Regularly rotate your tokens for security

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Token is invalid or expired
2. **403 Forbidden**: Rate limit exceeded or insufficient permissions
3. **404 Not Found**: Repository or resource doesn't exist

### Debug Steps

1. Verify token is set: `echo $GITHUB_API_TOKEN` (Linux/Mac) or `echo $env:GITHUB_API_TOKEN` (PowerShell)
2. Test token manually:
   ```bash
   curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user
   ```
3. Check N8N Builder logs for authentication messages
4. Ensure token has correct permissions for the repositories you're accessing

## API Endpoints Used

The MCP Research Tool uses these GitHub API endpoints:

- **Code Search**: `GET /search/code` - Search for code in repositories
- **Repository Content**: `GET /repos/{owner}/{repo}/contents/{path}` - Get file contents

All requests include proper authentication headers when a token is provided.
