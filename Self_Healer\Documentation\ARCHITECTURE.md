# Self-Healer System Architecture

## Overview

The Self-Healer system is designed to automatically detect, analyze, and resolve issues in the N8N Builder system. It operates as a continuous background service that monitors system health and proactively addresses problems using a comprehensive audit trail system.

## Core Components

### 1. Error Detection Layer

#### ErrorMonitor (`core/error_monitor.py`)
- **Purpose**: Continuously monitors error logs and system state
- **Key Features**:
  - File system watcher for `logs/errors.log` and `logs/n8n_builder.log`
  - Real-time error detection using simplified, reliable patterns
  - Integration with existing `EnhancedErrorHandler`
  - Pattern recognition for recurring issues
  - Severity assessment and prioritization
  - SelfHealed flag tracking to prevent duplicate processing
  - Healing action logging for complete audit trail

#### Error Classification
- Uses simplified, reliable error patterns that match consistent log format:
  ```python
  'error_log_entry': re.compile(r'\[.*?\]\s+ERROR\s+([^:]+):\s*(.+)', re.IGNORECASE)
  'critical_log_entry': re.compile(r'\[.*?\]\s+CRITICAL\s+([^:]+):\s*(.+)', re.IGNORECASE)
  ```
- Extracts module name and error message separately for better categorization
- Tracks error frequency and trends
- Identifies critical vs. recoverable errors
- Skips already-healed errors using SelfHealed flag detection

### 2. Context Analysis Layer

#### ContextAnalyzer (`core/context_analyzer.py`)
- **Purpose**: Gathers comprehensive context for detected errors
- **Key Features**:
  - Documentation retrieval from structured docs
  - Code context gathering using codebase retrieval
  - Dependency mapping and impact analysis
  - Historical error pattern analysis
  - System state assessment

#### Context Sources
- **Documentation**: Uses new Documentation/ structure
- **Code**: Leverages existing codebase retrieval engine
- **Logs**: Analyzes related log entries and patterns
- **System**: Monitors performance and resource metrics
- **History**: Reviews previous similar errors and solutions

### 3. Solution Generation Layer

#### SolutionGenerator (`core/solution_generator.py`)
- **Purpose**: Creates targeted solutions for detected errors
- **Key Features**:
  - Local LLM integration (mimo-vl-7b-rl)
  - Pattern-based solution templates
  - Multi-strategy approach (immediate, gradual, preventive)
  - Solution ranking and prioritization
  - Risk assessment for proposed changes

#### Solution Types
- **Immediate Fixes**: Quick patches for critical issues
- **Systematic Repairs**: Comprehensive solutions for root causes
- **Preventive Measures**: Changes to prevent future occurrences
- **Configuration Adjustments**: Parameter tuning and optimization
- **Code Improvements**: Refactoring and enhancement suggestions

### 4. Validation & Safety Layer

#### SolutionValidator (`core/solution_validator.py`)
- **Purpose**: Ensures solutions are safe and effective
- **Key Features**:
  - Isolated testing environment
  - Rollback capability for all changes
  - Impact assessment and risk evaluation
  - Integration with existing test suite
  - Gradual deployment with monitoring

### 5. Learning & Adaptation Layer

#### LearningEngine (`core/learning_engine.py`)
- **Purpose**: Continuously improves system performance
- **Key Features**:
  - Pattern recognition and machine learning
  - Success/failure analysis and correlation
  - Adaptive solution generation
  - Predictive error detection
  - Performance optimization insights

#### Learning Mechanisms
- **Solution Effectiveness**: Track success rates by error type
- **Pattern Recognition**: Identify new error patterns
- **Context Correlation**: Learn which contexts predict success
- **Preventive Insights**: Develop proactive measures
- **System Evolution**: Adapt to changing system characteristics

## SelfHealed Flag System Architecture

### Healing Action Tracking

The Self-Healer implements a comprehensive audit trail system that integrates seamlessly with the existing logging infrastructure:

#### Log Entry Format
```
[timestamp] INFO self_healer.action: SelfHealed - Error Title: One-sentence summary of action taken
[timestamp] INFO self_healer.action: SelfHealFailed - Error Title: Brief description of failure reason
```

#### Implementation Components

1. **Healing Action Logger** (`error_monitor.log_healing_action()`)
   - Appends healing results to main log file
   - Formats entries to match existing log structure
   - Truncates long descriptions to maintain readability

2. **Already-Healed Detection** (`error_monitor._is_error_already_healed()`)
   - Scans log lines for SelfHealed/SelfHealFailed markers
   - Prevents duplicate processing of resolved errors
   - Enables efficient restart and resume capability

3. **Statistics Extraction** (`error_monitor.get_healing_statistics_from_log()`)
   - Analyzes log file to extract healing metrics
   - Provides success rates and recent activity
   - Enables performance monitoring without external databases

### Benefits of Log-Based Tracking

- **Persistence**: Healing history survives system restarts and crashes
- **Transparency**: All healing actions are visible in standard log files
- **Integration**: Works seamlessly with existing log management tools
- **Simplicity**: No additional databases or storage systems required
- **Auditability**: Complete trail of all healing attempts and outcomes

### Data Flow Architecture

```
Error Detection → Already Healed? → Context Analysis → Solution Generation → Validation → Implementation → Log Healing Action → Learning
     ↑               ↓ (Skip)                                                                           ↓                    ↓
     ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←← Feedback Loop ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## Integration Architecture

### Existing System Integration

#### Logging System
- Extends `logging_config.py` with self-healer specific loggers
- Integrates with specialized loggers for different components
- Maintains compatibility with existing log rotation and management
- Adds SelfHealed flag tracking to standard log entries

#### Error Handling
- Builds upon `EnhancedErrorHandler` for error classification
- Uses simplified error patterns for reliable detection
- Maintains existing error reporting and user guidance
- Adds healing action tracking and duplicate prevention

#### Performance Monitoring
- Integrates with `performance_optimizer.py` for system metrics
- Uses existing performance tracking for solution validation
- Coordinates with monitoring for proactive issue detection

#### Project Management
- Uses `project_manager.py` for safe file operations
- Integrates with existing backup and versioning systems
- Maintains project integrity during healing operations

## Configuration and Control

### Configuration Files
- `config/healer_config.yaml`: Main configuration
- `config/project_config.yaml`: Project-specific settings
- Error patterns and solution templates embedded in code

### Control Interface
- REST API for manual control and monitoring
- Dashboard for real-time status and metrics at http://localhost:8081
- CLI commands for administration and debugging
- New API endpoints for healing statistics and log rescanning

### API Endpoints

#### Status and Monitoring
- `GET /api/status` - Current system status and metrics
- `GET /api/metrics` - Comprehensive system metrics
- `GET /api/sessions` - Healing session information
- `GET /api/learning` - Learning system statistics
- `GET /api/healing_stats` - Healing statistics from log analysis

#### Control Operations
- `POST /api/emergency_stop` - Emergency stop the healing system
- `POST /api/rescan_logs` - Force rescan of recent log entries

## Security and Safety

### Safety Measures
- All changes require validation before implementation
- Automatic rollback on failure detection
- Rate limiting to prevent system overload
- Emergency stop mechanisms for critical situations
- SelfHealed flag prevents duplicate processing

### Security Considerations
- Secure handling of system credentials and sensitive data
- Audit logging for all healing activities via SelfHealed flags
- Access control for manual interventions
- Isolation of testing environments

## Monitoring and Metrics

### Key Metrics
- Error detection rate and accuracy
- Solution success rate by category (tracked via SelfHealed flags)
- System healing time (detection to resolution)
- False positive/negative rates
- System stability improvements
- Healing activity frequency and patterns

### Log-Based Monitoring

#### Healing Activity Tracking
```bash
# View recent healing actions
grep "SelfHealed\|SelfHealFailed" logs/n8n_builder.log | tail -20

# Calculate success rate
total_attempts=$(grep -c "SelfHealed\|SelfHealFailed" logs/n8n_builder.log)
successful=$(grep -c "SelfHealed" logs/n8n_builder.log)
echo "Success rate: $((successful * 100 / total_attempts))%"
```

### Dashboards
- Real-time healing activity monitor
- Historical trends and patterns
- System health overview
- Performance impact assessment
- Healing statistics from log analysis

## Error Pattern Evolution

### Simplified Pattern Approach

The system has evolved from complex, specific regex patterns to simple, reliable ones:

#### Old Approach (Complex)
```python
'validation_error': re.compile(r'ERROR.*?validation.*?failed:\s*(.+)', re.IGNORECASE)
'workflow_generation_error': re.compile(r'ERROR.*?(?:generating|generation).*?workflow.*?:\s*(.+)', re.IGNORECASE)
```

#### New Approach (Simplified)
```python
'error_log_entry': re.compile(r'\[.*?\]\s+ERROR\s+([^:]+):\s*(.+)', re.IGNORECASE)
'critical_log_entry': re.compile(r'\[.*?\]\s+CRITICAL\s+([^:]+):\s*(.+)', re.IGNORECASE)
```

### Benefits of Simplified Patterns
- **Reliability**: Catches all errors regardless of specific content
- **Maintainability**: Fewer patterns to maintain and debug
- **Consistency**: Uniform parsing across all error types
- **Flexibility**: Works with any error message format
- **Performance**: Faster pattern matching

## Future Enhancements

### Planned Features
- Distributed healing across multiple instances
- Advanced ML models for pattern recognition
- Integration with external monitoring systems
- Automated documentation updates
- Predictive maintenance capabilities
- Enhanced SelfHealed flag analytics

### SelfHealed Flag Enhancements
- **Categorized Healing**: Different flag types for different solution categories
- **Severity Tracking**: Include healing difficulty/complexity in flags
- **Time-to-Heal Metrics**: Track resolution time in flag entries
- **Cross-Reference Linking**: Link healing actions to original error entries

This architecture provides a robust foundation for building a self-healing system that can automatically maintain and improve the N8N Builder system while ensuring safety, reliability, and complete auditability through the SelfHealed flag system.
