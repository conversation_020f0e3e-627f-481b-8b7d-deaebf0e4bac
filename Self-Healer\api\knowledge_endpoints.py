"""
KnowledgeBase API Endpoints for Self-Healer
Provides REST API access to Self-Healer knowledge stored in KnowledgeBase.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from ..core.knowledge_integration import get_knowledge_integrator, KnowledgeBaseIntegrator

logger = logging.getLogger(__name__)

# Create API router
knowledge_router = APIRouter(prefix="/api/selfhealer", tags=["knowledge"])


class SessionKnowledgeResponse(BaseModel):
    """Response model for session knowledge."""
    session_id: str
    entity_id: Optional[int]
    facts: List[Dict[str, Any]]
    evidence: List[Dict[str, Any]]
    attributes: List[Dict[str, Any]]
    knowledge_summary: Dict[str, Any]


class ErrorTypeKnowledgeResponse(BaseModel):
    """Response model for error type knowledge."""
    error_type: str
    facts: List[Dict[str, Any]]
    opinions: List[Dict[str, Any]]
    effectiveness_metrics: Dict[str, Any]
    recommendation: str


class SolutionEffectivenessResponse(BaseModel):
    """Response model for solution effectiveness."""
    total_solutions: int
    average_effectiveness: float
    top_solutions: List[Dict[str, Any]]
    trending_solutions: List[Dict[str, Any]]
    effectiveness_by_category: Dict[str, float]


@knowledge_router.get("/session/{session_id}", response_model=SessionKnowledgeResponse)
async def get_session_knowledge(session_id: str):
    """
    Get comprehensive knowledge about a specific healing session.
    
    Returns all facts, evidence, opinions, and cross-correlations related to the session.
    """
    try:
        integrator = get_knowledge_integrator()
        knowledge_data = await integrator.get_session_knowledge(session_id)
        
        if 'error' in knowledge_data:
            raise HTTPException(status_code=404, detail=knowledge_data['error'])
        
        return SessionKnowledgeResponse(**knowledge_data)
        
    except Exception as e:
        logger.error(f"Error retrieving session knowledge for {session_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@knowledge_router.get("/error-type/{error_type}", response_model=ErrorTypeKnowledgeResponse)
async def get_error_type_knowledge(error_type: str):
    """
    Get comprehensive knowledge about a specific error type.
    
    Returns all solutions, patterns, effectiveness ratings, and recommendations.
    """
    try:
        integrator = get_knowledge_integrator()
        knowledge_data = await integrator.get_error_type_knowledge(error_type)
        
        if 'error' in knowledge_data:
            raise HTTPException(status_code=404, detail=knowledge_data['error'])
        
        return ErrorTypeKnowledgeResponse(**knowledge_data)
        
    except Exception as e:
        logger.error(f"Error retrieving error type knowledge for {error_type}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@knowledge_router.get("/solutions/effectiveness", response_model=SolutionEffectivenessResponse)
async def get_solution_effectiveness(
    category: Optional[str] = Query(None, description="Filter by error category"),
    min_validity: Optional[float] = Query(0.0, description="Minimum validity rating"),
    limit: Optional[int] = Query(10, description="Maximum number of results")
):
    """
    Get solution effectiveness analytics and recommendations.
    
    Returns aggregated effectiveness data, top solutions, and trending patterns.
    """
    try:
        integrator = get_knowledge_integrator()
        
        # Build query based on parameters
        base_query = """
        SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
        """
        
        params = []
        
        if category:
            base_query += " AND f.Name LIKE ?"
            params.append(f"%{category}%")
        
        if min_validity > 0:
            base_query += " AND f.ValidityRating >= ?"
            params.append(min_validity)
        
        base_query += """
        GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
        ORDER BY f.ValidityRating DESC, f.CreateDate DESC
        """
        
        if limit:
            base_query += f" OFFSET 0 ROWS FETCH NEXT {limit} ROWS ONLY"
        
        result = await integrator.db_tool.execute_query(base_query, params)
        
        if not result['rows']:
            return SolutionEffectivenessResponse(
                total_solutions=0,
                average_effectiveness=0.0,
                top_solutions=[],
                trending_solutions=[],
                effectiveness_by_category={}
            )
        
        # Calculate metrics
        solutions = result['rows']
        total_solutions = len(solutions)
        average_effectiveness = sum(float(s['ValidityRating']) for s in solutions) / total_solutions
        
        # Get top solutions (highest validity)
        top_solutions = sorted(solutions, key=lambda x: float(x['ValidityRating']), reverse=True)[:5]
        
        # Get trending solutions (recent with good validity)
        trending_solutions = [
            s for s in solutions 
            if float(s['ValidityRating']) > 70.0 and 
            datetime.fromisoformat(s['CreateDate'].replace('T', ' ')) > datetime.now().replace(day=datetime.now().day-7)
        ][:5]
        
        # Calculate effectiveness by category
        effectiveness_by_category = await _calculate_effectiveness_by_category(integrator)
        
        return SolutionEffectivenessResponse(
            total_solutions=total_solutions,
            average_effectiveness=round(average_effectiveness, 2),
            top_solutions=top_solutions,
            trending_solutions=trending_solutions,
            effectiveness_by_category=effectiveness_by_category
        )
        
    except Exception as e:
        logger.error(f"Error retrieving solution effectiveness: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@knowledge_router.post("/knowledge/update")
async def update_knowledge_validity(
    fact_id: int,
    new_evidence: str,
    success: bool,
    data_source: str = "Manual Update"
):
    """
    Update knowledge validity based on new evidence.
    
    Adds new evidence and recalculates validity ratings.
    """
    try:
        integrator = get_knowledge_integrator()
        
        # Add new evidence
        evidence_query = """
        INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
        """
        
        evidence_name = f"Update_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        evidence_result = await integrator.db_tool.execute_query(evidence_query, [
            evidence_name, fact_id, new_evidence, data_source
        ])
        
        # Get current fact
        fact_query = "SELECT ValidityRating FROM REF_Fact WHERE ID = ?"
        fact_result = await integrator.db_tool.execute_query(fact_query, [fact_id])
        
        if not fact_result['rows']:
            raise HTTPException(status_code=404, detail="Fact not found")
        
        current_rating = float(fact_result['rows'][0]['ValidityRating'])
        
        # Calculate new rating based on evidence
        evidence_rating = 85.0 if success else 15.0
        
        # Get evidence count for weighted average
        count_query = "SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?"
        count_result = await integrator.db_tool.execute_query(count_query, [fact_id])
        evidence_count = count_result['rows'][0]['EvidenceCount']
        
        # Calculate weighted average
        new_rating = ((current_rating * (evidence_count - 1)) + evidence_rating) / evidence_count
        
        # Update fact validity
        update_query = "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?"
        await integrator.db_tool.execute_query(update_query, [new_rating, fact_id])
        
        return {
            "success": True,
            "fact_id": fact_id,
            "previous_rating": current_rating,
            "new_rating": round(new_rating, 2),
            "evidence_id": evidence_result['rows'][0]['ID'] if evidence_result['rows'] else None
        }
        
    except Exception as e:
        logger.error(f"Error updating knowledge validity: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@knowledge_router.get("/search")
async def search_knowledge(
    query: str = Query(..., description="Search query"),
    knowledge_type: Optional[str] = Query(None, description="Type: fact, opinion, evidence"),
    min_validity: Optional[float] = Query(0.0, description="Minimum validity rating"),
    limit: Optional[int] = Query(20, description="Maximum results")
):
    """
    Search across all Self-Healer knowledge in KnowledgeBase.
    
    Performs full-text search across facts, opinions, and evidence.
    """
    try:
        integrator = get_knowledge_integrator()
        results = {"facts": [], "opinions": [], "evidence": []}
        
        # Search facts
        if not knowledge_type or knowledge_type == "fact":
            facts_query = """
            SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
            """
            facts_result = await integrator.db_tool.execute_query(
                facts_query, [f"%{query}%", f"%{query}%", min_validity]
            )
            results["facts"] = facts_result['rows'][:limit//3 if not knowledge_type else limit]
        
        # Search opinions
        if not knowledge_type or knowledge_type == "opinion":
            opinions_query = """
            SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
            """
            opinions_result = await integrator.db_tool.execute_query(
                opinions_query, [f"%{query}%", f"%{query}%", f"%{query}%", min_validity]
            )
            results["opinions"] = opinions_result['rows'][:limit//3 if not knowledge_type else limit]
        
        # Search evidence
        if not knowledge_type or knowledge_type == "evidence":
            evidence_query = """
            SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
            """
            evidence_result = await integrator.db_tool.execute_query(
                evidence_query, [f"%{query}%", f"%{query}%", f"%{query}%"]
            )
            results["evidence"] = evidence_result['rows'][:limit//3 if not knowledge_type else limit]
        
        # Calculate search summary
        total_results = len(results["facts"]) + len(results["opinions"]) + len(results["evidence"])
        
        return {
            "query": query,
            "total_results": total_results,
            "results": results,
            "search_metadata": {
                "knowledge_type_filter": knowledge_type,
                "min_validity_filter": min_validity,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error searching knowledge: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _calculate_effectiveness_by_category(integrator: KnowledgeBaseIntegrator) -> Dict[str, float]:
    """Calculate average effectiveness by error category."""
    try:
        query = """
        SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
        """
        
        result = await integrator.db_tool.execute_query(query)
        
        return {
            row['Category']: round(float(row['AvgEffectiveness']), 2)
            for row in result['rows']
        }
        
    except Exception as e:
        logger.error(f"Error calculating effectiveness by category: {e}")
        return {}
