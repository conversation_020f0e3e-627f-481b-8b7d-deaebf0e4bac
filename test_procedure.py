#!/usr/bin/env python3
"""
Test the z_S_SYS_Admin_KnowledgeBaseSchema procedure
"""
import asyncio
import sys
sys.path.insert(0, '.')
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def test_procedure():
    db_tool = MCPDatabaseTool('knowledgebase')

    print('🔍 Testing z_S_SYS_Admin_KnowledgeBaseSchema procedure...')

    try:
        # First, let's check what tables exist
        print('\n=== Checking what tables exist ===')
        result = await db_tool.execute_query("""
            SELECT TABLE_NAME, TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)

        if result.get('status') == 'success':
            tables = result.get('rows', [])
            print(f'Found {len(tables)} tables:')
            for table in tables:
                print(f'  - {table}')

        # Now test the procedure
        print('\n=== Testing procedure ===')
        result = await db_tool.execute_query('EXEC z_S_SYS_Admin_KnowledgeBaseSchema')

        status = result.get('status')
        rows = result.get('rows', [])

        print(f'Status: {status}')
        print(f'Rows returned: {len(rows)}')

        if status == 'success':
            for i, row in enumerate(rows[:10]):  # Show first 10 rows
                print(f'Row {i+1}: {row}')
        else:
            error = result.get('error')
            print(f'Error: {error}')

        # Test with specific table
        print('\n=== Testing with specific table ===')
        result = await db_tool.execute_query("EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = 'REF_Entities'")

        status = result.get('status')
        rows = result.get('rows', [])

        print(f'Status: {status}')
        print(f'Rows returned: {len(rows)}')

        if status == 'success':
            for i, row in enumerate(rows[:5]):  # Show first 5 rows
                print(f'Row {i+1}: {row}')
        else:
            error = result.get('error')
            print(f'Error: {error}')

    except Exception as e:
        print(f'Exception: {e}')
        import traceback
        print(f'Traceback: {traceback.format_exc()}')

if __name__ == "__main__":
    asyncio.run(test_procedure())
